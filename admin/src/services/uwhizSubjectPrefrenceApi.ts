import axiosInstance from "../lib/axios";
import { subjectPrefrence } from "../lib/types";

//Get all Subject From Constant Table
export const getSubject = async (): Promise<any> => {
  try {
    const response = await axiosInstance.get("/constant/TuitionClasses");
    const tuitionClasses = response.data;
    const educationDetail = tuitionClasses.details?.find((d: any) => d.name === 'Education');
    const subjectSubDetail = educationDetail?.subDetails?.find((sd: any) => sd.name === 'Subject');
    const subjectValues = subjectSubDetail?.values?.map((val: any) => ({ id: val.id, value: val.name })) || [];

    return {
      success: true,
      data: { details: subjectValues }
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch subject data: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//insert into subjectPrefrenceTable
export const createsubjectPrefrence = async (
  data: subjectPrefrence
): Promise<any> => {
  try {
    const response = await axiosInstance.post("/subjectPrefrence",  data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create subject Prefrence: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//delete from subjectPrefrence
export const deleteSubjectPrefrence = async (id: string): Promise<any> => {
  try {
    const response = await axiosInstance.delete(`/subjectPrefrence/${id}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete subject Prefrence: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//get from subjectPrefrence
export const getSubjectPrefrence = async (examId: number): Promise<any> => {
  try {
    const response = await axiosInstance.get(`/subjectPrefrence/${examId}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch subject prefrence: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};