<?php

namespace ProfileViews\Repositories;

use ProfileViews\Models\ProfileViews;
use ProfileViews\Interfaces\ProfileViewsInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProfileViewsRepository implements ProfileViewsInterface
{
    protected $profileviews;

    function __construct(ProfileViews $profileviews)
    {
        $this->profileviews = $profileviews;
    }

    public function getAll($request)
    {
        $profileviews = DB::table('StudentClassViewLog')
            ->leftJoin('Classes', 'StudentClassViewLog.classId', '=', 'Classes.id')
            ->leftJoin('Student', 'StudentClassViewLog.studentId', '=', 'Student.id')
            ->select([
                'StudentClassViewLog.*',
                'Classes.firstName as class_firstname',
                'Classes.lastName as class_lastname',
                'Classes.className as class_name',
                'Student.firstName as student_firstname',
                'Student.lastName as student_lastname',
                'Student.email as student_email',
            ])
            ->where('StudentClassViewLog.classId', Auth::id());

        searchColumn($request->input('columns'), $profileviews);
        orderColumn($request, $profileviews, 'StudentClassViewLog.id');

        return $profileviews;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('student_name', function ($data) {
                $firstInitial = $data->student_firstname ? strtoupper(substr($data->student_firstname, 0, 1)) : '';
                $lastInitial = $data->student_lastname ? strtoupper(substr($data->student_lastname, 0, 1)) : '';
                return ($firstInitial || $lastInitial) ? "{$firstInitial}.{$lastInitial}." : 'N/A';
            })
            ->addColumn('student_email', function ($data) {
                if (!$data->student_email) return 'N/A';

                $parts = explode('@', $data->student_email);
                $username = $parts[0] ?? '';
                $domain = $parts[1] ?? 'hidden.com';

                $visible = substr($username, 0, 3);
                return $visible . '***@' . $domain;
            })
            ->addColumn('viewed_at', function ($data) {
                return $data->viewedAt ? \Carbon\Carbon::parse($data->viewedAt)->format('d M Y h:i A') : 'N/A';
            })
            ->addColumn('class_name', function ($data) {
                return $data->class_name ?? 'N/A';
            })
            ->editColumn('student_firstname', function () {
                return null;
            })
            ->editColumn('student_lastname', function () {
                return null;
            })
            ->editColumn('student_email', function ($data) {
                if (!$data->student_email) return 'N/A';

                $parts = explode('@', $data->student_email);
                $username = $parts[0] ?? '';
                $domain = $parts[1] ?? 'hidden.com';

                $visible = substr($username, 0, 3);
                return $visible . '***@' . $domain;
            })->editColumn('studentId', function () {
                return null;
            })
            ->rawColumns(['action'])
            ->make(true);
    }
}
