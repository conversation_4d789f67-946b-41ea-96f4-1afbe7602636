<?php

namespace ProfileViews\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Classes;

class ProfileViews extends Model
{
    use HasFactory;

    protected $table = 'StudentClassViewLog';

    protected $fillable = [
        'studentId',
        'classId',
        'viewedAt'
    ];

    public function classesDetails()
{
    return $this->belongsTo(Classes::class, 'classId', 'id');
}

public function studentDetails()
{
    return $this->belongsTo(Student::class, 'studentId', 'id');
}
}
