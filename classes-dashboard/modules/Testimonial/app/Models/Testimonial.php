<?php

namespace Testimonial\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Testimonial extends Model
{
    use HasFactory;

    protected $table = 'Testimonial';
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'classId',
        'message',
        'status',
        'rating',
        'updatedAt',
        'createdAt',
    ];
    public $timestamps = false;
}
