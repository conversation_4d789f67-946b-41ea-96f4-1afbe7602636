<!-- fields.blade.php -->
<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('document_name', 'Document Name *', ['class' => 'form-label']) !!}
            {!! Form::text('document_name', null, ['class' => 'form-control', 'placeholder' => 'Enter Document Name', 'required' => 'required']) !!}
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('file', 'Document File (PDF, Image, or TXT Only) *', ['class' => 'form-label']) !!} 
            <br/>
            {!! Form::file('file', ['class' => 'form-control-file', 'accept' => '.pdf,.jpg,.jpeg,.png,.txt', 'required' => 'required']) !!} 
        </div>
    </div>
    @php
    $rawCategories = \DB::table('document_categories')->select('id', 'category_name', 'is_optional')->get();
    $categories = [];

    foreach ($rawCategories as $cat) {
        $categories[$cat->id] = $cat->category_name . ($cat->is_optional ? ' (Optional)' : '');
    }
    @endphp

    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('category_id', 'Category *', ['class' => 'form-label']) !!}
            {!! Form::select('category_id', $categories, null, [
                'class' => 'form-control',
                'id' => 'category_id',
                'required' => 'required',
                'placeholder' => 'Select a category'
            ]) !!}
        </div>
    </div>
    <div class="col-md-12" id="other_category_group" style="display: none;">
        <div class="form-group">
            {!! Form::label('other_category', 'Other Category *', ['class' => 'form-label']) !!}
            {!! Form::text('other_category', null, ['class' => 'form-control', 'id' => 'other_category', 'placeholder' => 'Enter custom category']) !!}
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('description', 'Description', ['class' => 'form-label']) !!}
            {!! Form::textarea('description', null, ['class' => 'form-control', 'placeholder' => 'Enter description (optional)', 'rows' => 3]) !!}
        </div>
    </div>
    <div class="col-md-12" style="display: none;">
        <div class="form-group">
            {!! Form::hidden('student_id', isset($data) ? $data->id : (request()->student_id ?? '')) !!}
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::submit('Submit', ['id' => 'savedocuments', 'class' => 'btn btn-primary']) !!}
            <button type="button" class="btn btn-secondary ml-2" data-dismiss="modal">Cancel</button>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('#category_id').select2({
            placeholder: 'Select a category',
            allowClear: true,
            closeOnSelect: true
        }).on('change', function() {
            if ($(this).val() == 13) {
                $('#other_category_group').show();
                $('#other_category').attr('required', 'required');
            } else {
                $('#other_category_group').hide();
                $('#other_category').removeAttr('required').val('');
            }
        });
    });
</script>