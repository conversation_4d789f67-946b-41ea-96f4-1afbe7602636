<?php

namespace AnnualCalendar\Http\Controllers;

use Admission\Models\StudentAcademicInfo;
use App\Http\Controllers\Controller;
use Event\Models\Event;
use Holiday\Models\Holiday;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AnnualCalenderController extends Controller
{
    public function annual_calendarview()
    {
        return view('AnnualCalendar::index');
    }

    public function calendar_json(Request $request)
    {
        $res = [];

        if ($request->class_id) {
            $classId = $request->get('academic_info')->class_uuid;
        } else {
            $classId = Auth::id();
        }

        $birthOrAnni = StudentAcademicInfo::where('class_uuid', $classId)
            ->leftJoin('student_details_view', 'student_academic_info.id', '=', 'student_details_view.student_academic_id')
            ->orderByRaw("EXTRACT(MONTH FROM student_details_view.date_of_birth), EXTRACT(DAY FROM student_details_view.date_of_birth)")
            ->select('student_details_view.id', 'student_details_view.first_name', 'student_details_view.last_name', 'student_details_view.date_of_birth')
            ->get();

        foreach ($birthOrAnni as $badata) {
            $year = date('Y');
            $birthDay = date('m-d', strtotime($badata->date_of_birth));

            $birthdate = $year . '-' . $birthDay;

            $res[] = [
                'title' => $badata->first_name . " " . $badata->last_name,
                'start' => $birthdate,
                'end' => date("Y-m-d", strtotime("+1 day", strtotime($birthdate))),
                'color' => '#17a2b8',
                'cid' => "4",
                'type' => "Birthdays"
            ];
        }

        $events = Event::where('class_uuid', Auth::id())->orderByDesc('id');
        if ($request->start) {
            $events->where('date', '>=', $request->start);
        }

        if ($request->end) {
            $events->where('date', '<=', $request->end);
        }

        foreach ($events->get() as $edata) {
            $res[] = [
                'title' => $edata->event_name,
                'start' => date("Y-m-d", strtotime($edata->date)),
                'end' => date("Y-m-d", strtotime("+1 day", strtotime($edata->date))),
                'color' => '#ffc107',
                'cid' => "3",
                'type' => "Event"
            ];
        }

        $holidays = Holiday::where('class_uuid', Auth::id())
            ->select('date', 'holiday_name')
            ->get();

        foreach ($holidays as $hdata) {
            $res[] = [
                'title' => $hdata->holiday_name,
                'start' => date("Y-m-d", strtotime($hdata->date)),
                'end' => date("Y-m-d", strtotime("+1 day", strtotime($hdata->date))),
                'color' => '#28a745',
                'cid' => "2",
                'type' => "Holiday"
            ];
        }

        return response()->json($res);
    }
}
