<?php

namespace Fees\Http\Controllers;

use App\Http\Controllers\Controller;
use Classroom\Repositories\ClassroomRepository;
use Fees\Http\Requests\CreateClassroomWiseFeesRequest;
use Fees\Models\FeeType;
use Fees\Repositories\CategoryRepository;
use Fees\Repositories\ClassroomWiseFeeRepository;
use Illuminate\Http\Request;
use Years\Models\Years;

class ClassroomWiseFeeController extends Controller
{
    protected $ClassroomWiseFeeRepository;
    protected $classroomRepository;
    protected $categoryRepository;

    public function __construct(CategoryRepository $categoryRepository, ClassroomWiseFeeRepository $ClassroomWiseFeeRepository, ClassroomRepository $classroomRepository)
    {
        $this->middleware('permission:manage classroom fees', ['only' => ['index', 'create', 'edit', 'store' , 'destroy']]);
        $this->ClassroomWiseFeeRepository = $ClassroomWiseFeeRepository;
        $this->classroomRepository = $classroomRepository;
        $this->categoryRepository = $categoryRepository;
    }

    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->classroomRepository->getAll($request);
            return $this->ClassroomWiseFeeRepository->getDatatable($list);
        }

        return view('Fees::ClassroomWiseFee.index');
    }

    public function edit(Request $request, $id)
    {
        $category = $this->categoryRepository->getAll();
        $classroom = $this->classroomRepository->getClassroomById($id);
        $feesdetail = $this->ClassroomWiseFeeRepository->getFeesByClassroom($classroom->id);
        $feeType = FeeType::get();
        return view('Fees::ClassroomWiseFee.edit', compact('category', 'classroom', 'feesdetail', 'feeType'));
    }

    public function store(CreateClassroomWiseFeesRequest $request)
    {
       $this->ClassroomWiseFeeRepository->store($request->all());
       return response()->json(['success' => 'ClassroomWiseFee Created Successfully!!']);
    }

    public function destroy($id)
    {
        $event = $this->ClassroomWiseFeeRepository->getById($id);
        $event->delete();
        return response()->json(['success' => 'ClassroomWiseFee deleted successfully!!']);
    }

    public function lock($id)
    {
        $this->ClassroomWiseFeeRepository->lock($id);
        return response()->json(['success' => 'ClassroomWiseFee Fees Locked successfully!!']);
    }
}
