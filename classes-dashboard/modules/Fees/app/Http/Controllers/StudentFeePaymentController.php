<?php

namespace Fees\Http\Controllers;

use Admission\Repositories\AdmissionRepository;
use Fees\Repositories\CategoryRepository;
use Fees\Repositories\ClassroomWiseFeeRepository;
use Fees\Repositories\StudentFeePaymentRepository;
use Illuminate\Routing\Controller;
use Illuminate\Http\Request;

class StudentFeePaymentController extends Controller
{
    protected $admissionRepository;

    protected $yearsRepository;
    protected $classroomWiseFeeRepository;
    protected $studentFeePaymentRepository;
    protected $categoryRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        StudentFeePaymentRepository $studentFeePaymentRepository,
        ClassroomWiseFeeRepository $classroomWiseFeeRepository,
        CategoryRepository $categoryRepository
    ) {
        $this->middleware('permission:manage student fees', ['only' => ['setupStudentFees',
        'payStudentFee', 'destroyPaymentLog', 'studentPaymentReceipt', 'getAllStudentPaymentLogs']]);
        $this->middleware('permission:export student fee data', ['only' => ['exportFeesPaymentLogs',
        'exportDetailedFeesPaymentLogs']]);

        $this->admissionRepository = $admissionRepository;
        $this->classroomWiseFeeRepository = $classroomWiseFeeRepository;
        $this->studentFeePaymentRepository = $studentFeePaymentRepository;
        $this->categoryRepository = $categoryRepository;
    }


    public function setupStudentFees($id)
    {
        $studentdata = $this->admissionRepository->findByid($id);
        $category = $this->categoryRepository->getAll();
        $feesdetail = $this->studentFeePaymentRepository->getFeesByStudent($studentdata->id);

        return view('Admission::feesetup', compact('studentdata', 'feesdetail', 'category'));
    }

    public function payStudentFee(Request $request)
    {
       
        $this->studentFeePaymentRepository->payStudentFee($request->all());
        return response()->json(['success' => 'Fees paid Successfully !!']);
    }

    public function destroyPaymentLog($paymentid)
    {
        $this->studentFeePaymentRepository->getStudentPaymentLogsByPaymentid($paymentid)->delete();
        return response()->json(['success' => 'Payment Details Deleted Successfully']);
    }

    public function studentPaymentReceipt($id)
    {
        $paymentdetails = $this->studentFeePaymentRepository->getStudentPaymentLogsByPaymentid($id);
        $studentdata = $this->admissionRepository->findByid($paymentdetails->student_id);
        return view('Admission::payslip', compact('paymentdetails', 'studentdata'));
    }

    public function getPaidFees($student_id, $installmentName)
    {
        $data = $this->studentFeePaymentRepository->getPaidFees($student_id, $installmentName);
        return $data;
    }

    public function feesDiscount(Request $request, $student_id)
    {
       
        $status = $this->studentFeePaymentRepository->feesDiscount($request->all(), $student_id);
        if ($status) {
            return response()->json(['success' => 'Fees Discount Added Successfully !!']);
        } else {
            return response()->json(['errors' => 'Classroom Fee is not locked !!']);
        }
    }

    public function getAllStudentPaymentLogs(Request $request)
    {
        $department = departmentForStudentPortal();
        if (request()->ajax()) {
            $list = $this->studentFeePaymentRepository->getAll($request);
            return $this->studentFeePaymentRepository->getStudentPaymentDatatable($list);
        }
        return view('Fees::FeesCollection.index', compact('department'));
    }

    public function exportFeesPaymentLogs(Request $request) 
    {    
        $payments = $this->studentFeePaymentRepository->getAll($request)->get();
        return commonExport($payments, 'Fees::FeesCollection.export', 'paymentLogs');
    }

    public function exportDetailedFeesPaymentLogs(Request $request) 
    {    
        $payments = $this->studentFeePaymentRepository->getStudentsFeesByClassroom($request->all());
        return commonExport($payments, 'Fees::FeesCollection.exportDetailed', 'DetailedFees');
    }
}