<table>
    <thead>
        @php
            $categoryCount = 0;
        @endphp

        @if (isset($datas))
        
            @foreach ($datas as $data)
                @foreach ($data as $installment)
                    @php
                        $categoryData = collect($installment['installment_data'])->mapWithKeys(function ($item) {
                            return [$item['fees_category_id'] => $item];
                        });
                        $categoryCount++;
                    @endphp
                @endforeach
            @endforeach
        @endif
    </thead>
    <tbody>
        <tr></tr>
        @if (isset($datas))
            @foreach ($datas as $studentId => $studentFees)
                @if (isset($studentFees))
                    <tr>
                        <td colspan="{{ 6 + $categoryCount }}"><b>Student Name : </b>
                            {{ $studentFees[0]['first_name'] . ' ' . $studentFees[0]['middle_name'] . ' ' . $studentFees[0]['last_name'] }}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="{{ 6 + $categoryCount }}"><b>GRNo : </b> {{ $studentFees[0]['gr_no'] }}</td>
                    </tr>
                    <tr>
                        <td colspan="{{ 6 + $categoryCount }}"><b>ClassName : </b> {{ $studentFees[0]['class_name'] }}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="{{ 6 + $categoryCount }}"><b>Year : </b> {{ $studentFees[0]['year_name'] }}</td>
                    </tr>

                    <tr>
                        <td><b>Cycle Date</b></td>
                        <td><b>Cycle Lable</b></td>
                        @foreach ($categoryData as $cat)
                            <td>
                                <b>{{ $cat['fees_category'] }}</b>
                            </td>
                        @endforeach
                        <td><b>Transport Fee</b></td>
                        <td><b>Total Amount</b></td>
                        <td><b>Total Paid Amount</b></td>
                        <td><b>Due Amount</b></td>
                    </tr>

                    @foreach ($studentFees as $installment)
                        <tr>
                            <td>{{ date_formatter($installment['cycle_date']) }}</td>
                            <td>{{ $installment['installment_name'] }}</td>
                            @foreach ($categoryData as $cat)
                                <td>{{ $cat['amount'] ?? 0 }}</td>
                            @endforeach

                            <td>
                                {{ $installment['transport_fee'] }}
                            </td>
                            <td>
                                {{ $installment['total_amount'] }}
                            </td>
                            <td>
                                {{ $installment['paid_amount'] ?? 0 }}
                            </td>
                            <td>
                                {{ $installment['due_amount'] }}
                            </td>
                        </tr>
                    @endforeach
                @endif
                <tr></tr>
                <tr></tr>
            @endforeach
        @endif
    </tbody>
</table>
