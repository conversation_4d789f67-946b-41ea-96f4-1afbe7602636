<?php

namespace Subject\Repositories;

use Subject\Models\Subject;
use Subject\Interfaces\SubjectInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SubjectRepository implements SubjectInterface
{
    protected $subject;

    function __construct(Subject $subject)
    {
        $this->subject = $subject;
    }

    public function getAll($request)
    {
        $list = $this->subject->where('subjects.class_uuid', Auth::id())

        ->when($request->classroom, function ($query) use ($request) {
            return $query->where('classroom_id', $request->classroom);
        })
        ->when($request->department, function ($query) use ($request) {
            return $query->where('department.id', $request->department);
        })->where('subjects.year_id', getActiveYearId())
        ->leftJoin('classrooms', 'classrooms.id', '=', 'subjects.classroom_id')
        ->leftJoin('department', 'classrooms.department_id', '=', 'department.id')
        ->select(
            'subjects.*',
            'classrooms.class_name as class_name',
            'department.name as dept_name',
            DB::raw("CONCAT(classrooms.class_name, ' (', department.name, ')') AS class_info"),
        );

        $concatenatedColumns = ['class_info'];
        searchColumn($request->input('columns'), $list, $concatenatedColumns);
        orderColumn($request, $list, 'subjects.id');

        return $list;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
        ->addColumn('subject_name', function ($data) {
            $name = $data->subject_name;
            return $name;
        })
        ->addColumn('class_info', function ($data) {
            $cname = $data->class_info;
            return $cname;
        })
        ->addColumn('action', function ($data) {
            $button = '';
            if (Auth::user()->can('update subject')) {
                $button .= '<button data-editsubjectid="' . $data->id . '" data-toggle="modal" title="Edit" data-target="#newSubjectEntry" class="btn editsubjectEntry"><i class="fas fa-edit"></i></button>';
            }
            if (Auth::user()->can('delete subject')) {
                $button .= '<button type="button" class="deleteSubjectEntry btn" title="Delete" data-deletesubjectid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
            }
            return $button;
        })->rawColumns(['File', 'action'])
        ->make(true);
    }

    public function storeSubject($request)
    {
        $subject = $this->subject::create([
            'subject_name' => $request->subject_name,
            'classroom_id' => $request->classroom,
            'year_id' => getActiveYearId(),
            'class_uuid' => Auth::id(),
        ]);
    }

    public function updateSubject($request, $id)
    {
        $subject = $this->getSubjectById($id);
        $subject->subject_name = $request->subject_name;
        $subject->classroom_id = $request->classroom;
        $subject->save();
    }

    public function getSubjectById($id)
    {
        return $this->subject::Find($id);
    }

    public function getSubjectListByClassroom($request)
    {
        return $this->subject::with('classroom_name')->whereIn('classroom_id', $request->classroom)->get();
    }

    public function getSubjectFromClassroom($classroom_id)
    {
        return $this->subject::where('classroom_id', $classroom_id)->where('year_id', getActiveYearId())->get();
    }

    public function getAllSubjects()
    {
        return $this->subject::with('classroom_name','classroom_name.department_name')
        ->where('year_id', getActiveYearId())->get();
    }
}
