<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateStudentDetailsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            "firstName"    => "required|string|max:200",
            "middleName"    => "required|string|max:200",
            "lastName"    => "required|string|max:200",
            "mothersName"    => "required|string|max:200",
            "gender"    => "required|string|max:200",
            'aadhaar_no' => 'nullable|numeric|digits_between:1,15',
            'department' => 'required|string',
            'year' => 'required|string',
            'classroom' => 'required|string',

            "birthday"    => "required|string|max:200",
            "contact"    => "required|numeric|digits_between:7,13",
            "contactNo2"    => "nullable|numeric|digits_between:7,13",
            'email' => 'nullable|email|max:200',
            "address"    => "required|string|max:500",
            "school"    => "required|string|max:500",
            "medium"    => "required|string|max:200",
            "classroom_name"    => "required|string|max:200",

            'religion' => 'nullable|string|max:200',
            'caste' => 'nullable|string|max:200',
            'subCaste' => 'nullable|string|max:200',
            'birthPlace' => 'nullable|string|max:200',
            'motherTongue' => 'nullable|string|max:200',
            'bloodGroup' => 'nullable|string|max:10',
            'age' => 'nullable|integer|min:1|max:100',
        ];

        if (request()->hasFile('photo')) {
            $rules = array_merge($rules, [
                'photo' => 'mimes:jpg,jpeg,png|max:4096',
            ]);
        }

        return $rules;
    }
}
