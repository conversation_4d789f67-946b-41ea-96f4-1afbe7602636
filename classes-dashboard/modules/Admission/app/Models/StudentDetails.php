<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class StudentDetails extends Model
{
    protected $table = 'StudentProfile';

    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;
    public $timestamps = false;


    protected $fillable = [
        'id',
        'studentId',
        'birthday',
        'school',
        'address',
        'contactNo2',
        'photo',
        'documentUrl',
        'gender',
        'age',
        'aadhaarNo',
        'bloodGroup',
        'birthPlace',
        'motherTongue',
        'religion',
        'caste',
        'subCaste',
        'medium',
        'classroom',
        'status',
        'updatedAt',
        'createdAt'
    ];


    protected static function boot()
    {
        parent::boot();

        // Generate UUID on creation only
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = Str::uuid()->toString();
            }
        });
    }
}

