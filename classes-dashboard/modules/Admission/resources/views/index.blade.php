@extends('layouts.app')
@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Students</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <h3 class="box-title popup-title m-0">Filter Students Data</h3>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Department</b></label>
                                    <select id="department" class="form-control select2">
                                        <option value="">Select department</option>
                                        @foreach ($department as $value)
                                        <option value="{{ $value->id }}">{{ $value->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Classroom</b></label>
                                    <select id="classroom-filter" class="form-control select2 classroom-data">
                                        <option value="">Select Classroom</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><b>Status</b></label>
                                    <select id="status-filter" class="form-control select2">
                                        <option value="">Select Classroom</option>
                                        <option selected value="ACTIVE">ACTIVE</option>
                                        <option value="INACTIVE">INACTIVE</option>
                                    </select>
                                </div>
                            </div>
                            <input id="enq_status" type="hidden" value="{{ config('constants.STATUS.OPEN') }}" />
                            <div class="col-md-3">
                                <div class="form-group">
                                    <button id="filter" type="submit" class="btn btn-primary filter-btn">Filter</button>
                                    <button id="filterreset" class="btn btn-secondary filter-btn">Reset</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('create student')
                            <a href="{{ route('student.create') }}" class="btn btn-primary">
                                <i class="fa fa-plus-square"></i> Add New Students
                            </a>
                            @endcan
                            @can('export student data')
                            <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i>&nbsp; Export</button>
                            @endcan
                        </div>
                        <table id="admissions_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Photo</th>   
                                    <th>Students Name</th>
                                    <th>Contact No</th>
                                    <th>Year</th>
                                    <th>Classroom</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Photo</th>   
                                    <th>Students Name</th>
                                    <th>Contact No</th>
                                    <th>Year</th>
                                    <th>Classroom</th>
                                </tr>
                            </tfoot>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="modal" id="newWaypointEntry" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="createContent"></div>
            </div>

        </div>
    </div>
</div>
@endsection
@section('scripts')
<script>
    var admissionRoute = {
        index: "{{ route('student.index') }}",
        edit: "{{ route('student.edit',':editdid') }}",
        delete: "{{ route('student.destroy',':admissionsID') }}",
        getclassroomlist: "{{route('classroom.ajax.list')}}",
        export: "{{ route('export-student') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/Admission/js/index.js')) }}"></script>
@endsection