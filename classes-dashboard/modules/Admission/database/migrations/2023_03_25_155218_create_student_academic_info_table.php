<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStudentAcademicInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('student_academic_info', function (Blueprint $table) {
            $table->id();
            $table->text('student_id')->nullable();
             $table->string('class_uuid')->nullable();
            $table->integer('department')->nullable();
            $table->integer('classroom')->nullable();
            $table->integer('waypoint')->nullable();
            $table->integer('route')->nullable();
            $table->integer('year')->nullable();
            $table->string('status')->default('ACTIVE');
            $table->date('joining_date')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('student_academic_info');
    }
}
