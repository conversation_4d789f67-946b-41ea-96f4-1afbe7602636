<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Model;

class ClassesLogo extends Model
{
    protected $table = 'ClassesAbout';

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'classesLogo',
        'profilePhoto',
        'classId'
    ];

}
