<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Symfony\Component\Process\Process;
use App\Http\APIControllers\StudentAPIController;
use Admission\Http\Controllers\AdmissionController;
use AnnualCalendar\Http\Controllers\AnnualCalenderController;
use App\Http\APIControllers\LeaveAPIController;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});


Route::middleware('student.auth')->group(function () {
    Route::prefix('student')->group(function () {
        Route::get('get-student-details', [StudentAPIController::class, 'getStudentDetails']);
        Route::get('get-timetable', [StudentAPIController::class, 'getTimetable']);
        Route::get('get-attendance', [StudentAPIController::class, 'getAttendance']);
        Route::get('get-discipline-issue', [StudentAPIController::class, 'getDisciplineIssue']);
        Route::get('get-classwork', [StudentAPIController::class, 'getClassWork']);
        Route::get('get-homework', [StudentAPIController::class, 'getHomeWork']);
        Route::get('get-calender', [AnnualCalenderController::class, 'calendar_json']);
        Route::get('get-circulars', [StudentAPIController::class, 'getCirculars']);
        Route::get('check-student-in-class', [StudentAPIController::class, 'checkIfStudentInClass']);
        Route::get('get-documents', [StudentAPIController::class, 'getDocuments']);

        Route::get('get-leavelist', [LeaveAPIController::class, 'index']);
        Route::post('create-leave', [LeaveAPIController::class, 'store']);
        Route::put('update-leave/{id}', [LeaveAPIController::class, 'update']);
        Route::delete('delete-leave/{id}', [LeaveAPIController::class, 'destroy']);
        Route::get('get-leave-by-id/{id}', [LeaveAPIController::class, 'getLeaveById']);
    });
});

Route::get('/students-all-raw', function (Request $request) {
    $apiKey = $request->header('X-API-KEY');

    if ($apiKey !== env('SCHOOL_CREATION_API_KEY')) {
        return response()->json(['error' => 'Unauthorized'], 403);
    }

    return app(AdmissionController::class)->getAllStudentsRawAPI($request);
});
