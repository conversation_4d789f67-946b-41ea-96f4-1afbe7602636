<nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <ul class="navbar-nav">
        <li class="nav-item">
            <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
    </ul>

    <ul class="navbar-nav ml-auto">
        @can('read student')
            <!-- New Button for Sidebar Icons Modal -->
            <li class="nav-item">
                <a class="nav-link" href="#" data-toggle="modal" data-target="#sidebarIconsModal">
                    <i class="fas fa-th"></i> <!-- Icon for the button (grid layout icon) -->
                </a>
            </li>
            <li class="nav-item">
                <a id="openStudentGlobalModal" data-toggle="modal" data-target="#studentGlobalModal" href="#"
                    class="nav-link">
                    <i class="nav-icon fas fa-search"></i>
                </a>
            </li>
        @endcan
        <li class="nav-item user-menu">
            <select id="yearChange">
                @php
                    $years = geAllYear();
                    $activeYear = getActiveYearId();
                @endphp

                @foreach ($years as $year)
                    @if ($activeYear == $year->id)
                        <option selected value="{{ $year->id }}">{{ $year->year_name }}</option>
                    @else
                        <option value="{{ $year->id }}">{{ $year->year_name }}</option>
                    @endif
                @endforeach
            </select>
        </li>
        <li class="nav-item">
            <a href="" class="nav-link">
            </a>
        </li>
        <li>
            <input type="checkbox" class="sr-only" id="darkmode-toggle">
            <label for="darkmode-toggle" class="toggle">
                <span>Toggle dark mode</span>
            </label>
        </li>
        <li class="nav-item dropdown user-menu">
            <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
                <img src="{{ env('UEST_FRONTEND_URL') . '/' . Auth::user()->about->profilePhoto }}"
                    class="user-image img-circle elevation-2"> <span
                    class="d-none d-md-inline">{{ Auth::user()->firstName }} {{ Auth::user()->lastName }}</span>
            </a>

            <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
                <!-- User image -->
                <li class="user-header bg-primary">
                    <img src="{{ env('UEST_FRONTEND_URL') . '/' . Auth::user()->about->classesLogo }}"
                        class="img-circle elevation-2">
                    <p>
                        {{ Auth::user()->className }}
                        <small>Member since {!! date('M. Y', strtotime(Auth::user()->createdAt)) !!}</small>
                    </p>
                </li>
                <!-- Menu Footer-->
                <li class="user-footer">
                    @if (!Auth::user()->getIsSuperAdmin())
                        <a href="{{ route('users.show', Auth::id()) }}" class="btn btn-primary">Profile</a>
                    @endif
                    <a href="{!! url('/logout') !!}" class="btn btn-primary float-right"
                        onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        Sign out
                    </a>
                    <form id="logout-form" action="{{ url('/logout') }}" method="POST" style="display: none;">
                        {{ csrf_field() }}
                    </form>
                </li>
            </ul>
        </li>
    </ul>
</nav>
<aside class="main-sidebar sidebar-dark-primary elevation-4">
    <a href="#" class="brand-link">

        <span class="brand-text font-weight-light tracking-wider font-weight-bold"
            style="letter-spacing: 0.5rem; ">UEST</span> </a>
    <div class="sidebar">
        <div class="user-panel">
            <div class="pull-left image">
                <img src="{{ env('UEST_FRONTEND_URL') . '/' . Auth::user()->about->classesLogo }}"
                    class="user-image img-circle">
            </div>
            <div class="pull-left info">
                <p>{{ Auth::user()->className }}</p>
            </div>
        </div>
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu"
                data-accordion="false">
                <li class="nav-item">
                    <a href="{{ route('home') }}" class="nav-link {{ request()->is('/') ? 'active' : '' }}">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>
                            Dashboard
                        </p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('annualcalendarview') }}"
                        class="nav-link {{ request()->is('annual-calendar') ? 'active' : '' }}">
                        <i class="fa nav-icon  fas fa-calendar-week"></i>
                        <p>
                            Annual Calendar
                        </p>
                    </a>
                </li>

                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <i class="nav-icon fa fa-cog"></i>
                        <p>
                            Academic Setup
                            <i class="right fas fa-angle-left"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        @can('read year')
                            <li class="nav-item">
                                <a href="{{ route('years.index') }}"
                                    class="nav-link {{ request()->is('years') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-calendar"></i>
                                    <p>
                                        Year
                                    </p>
                                </a>
                            </li>
                        @endcan
                        @can('read department')
                            <li class="nav-item">
                                <a href="{{ route('department.index') }}"
                                    class="nav-link {{ request()->is('department') ? 'active' : '' }}">
                                    <i class="nav-icon fa fa-sitemap"></i>
                                    <p>
                                        Department
                                    </p>
                                </a>
                            </li>
                        @endcan
                        @can('read classroom')
                            <li class="nav-item">
                                <a href="{{ route('classroom.index') }}"
                                    class="nav-link {{ request()->is('classroom') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-restroom"></i>
                                    <p>Classroom</p>
                                </a>
                            </li>
                        @endcan
                        @can('read subject')
                            <li class="nav-item">
                                <a href="{{ route('subject.index') }}"
                                    class="nav-link {{ request()->is('subject') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-book"></i>
                                    <p>Subjects</p>
                                </a>
                            </li>
                        @endcan
                        @can('read resource')
                            <li class="nav-item">
                                <a href="{{ route('resource.index') }}"
                                    class="nav-link {{ request()->is('resource') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-school"></i>
                                    <p>Resources</p>
                                </a>
                            </li>
                        @endcan
                        @can('read timeslot')
                            <li class="nav-item">
                                <a href="{{ route('timeslots.index') }}"
                                    class="nav-link {{ request()->is('timeslots') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-clock"></i>
                                    <p>Timeslots</p>
                                </a>
                            </li>
                        @endcan
                        @can('read event')
                            <li class="nav-item">
                                <a href="{{ route('event.index') }}"
                                    class="nav-link {{ request()->is('event*') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-candy-cane"></i>
                                    <p>
                                        Events
                                    </p>
                                </a>
                            </li>
                        @endcan

                        @can('read holiday')
                            <li class="nav-item">
                                <a href="{{ route('holiday.index') }}"
                                    class="nav-link {{ request()->is('holiday*') ? 'active' : '' }}">
                                    <i class="nav-icon fa fa-h-square"></i>
                                    <p>
                                        Holiday
                                    </p>
                                </a>
                            </li>
                        @endcan

                        @can('manage timetable')
                            <li class="nav-item">
                                <a href="{{ route('batches.index') }}"
                                    class="nav-link {{ request()->is('batches*') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-calendar-plus"></i>
                                    <p>
                                        Create Batches
                                    </p>
                                </a>
                            </li>
                        @endcan

                        @can('manage timetable')
                            <li class="nav-item">
                                <a href="{{ route('batches.view') }}"
                                    class="nav-link {{ request()->is('view-batch*') ? 'active' : '' }}">
                                    <i class="nav-icon fas fa-calendar-plus"></i>
                                    <p>
                                        View Batches
                                    </p>
                                </a>
                            </li>
                        @endcan
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="nav-icon fa fa-cog"></i>
                                <p>
                                    Fee Setup
                                    <i class="right fas fa-angle-left"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                @can('read fees category')
                                    <li class="nav-item">
                                        <a href="{{ route('feesmanager.category.index') }}"
                                            class="nav-link {{ request()->is('feesmanager/category*') ? 'active' : '' }}">
                                            <i class="nav-icon fa fa-sitemap"></i>
                                            <p>
                                                Fees Category
                                            </p>
                                        </a>
                                    </li>
                                @endcan
                                @can('manage classroom fees')
                                    <li class="nav-item">
                                        <a href="{{ route('feesmanager.classroom-wise-fee.index') }}"
                                            class="nav-link {{ request()->is('feesmanager/classroom-wise-fee**') ? 'active' : '' }}">
                                            <i class="nav-icon fas fa-restroom"></i>
                                            <p>
                                                Classroom Fees
                                            </p>
                                        </a>
                                    </li>
                                @endcan

                                @can('manage classroom fees')
                                    <li class="nav-item">
                                        <a href="{{ route('enquiry.fee.setup.view') }}"
                                            class="nav-link {{ request()->is('enquiry-fee-setup*') ? 'active' : '' }}">
                                            <i class="nav-icon fas fa-restroom"></i>
                                            <p>
                                                Enquiry Fees
                                            </p>
                                        </a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    </ul>
                </li>

                <h3 class="main-title">Student</h3>
                @can('read enquiry')
                    <li class="nav-item">
                        <a href="{{ route('enquiry.index') }}"
                            class="nav-link {{ request()->is('enquiry') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-question-circle"></i>
                            <p>
                                Enquiry Management
                            </p>
                        </a>
                    </li>
                @endcan
                @can('read student')
                    <li class="nav-item">
                        <a href="{{ route('student.index') }}"
                            class="nav-link {{ request()->is('student') ? 'active' : '' }}">
                            <i class="nav-icon fa fa-university"></i>
                            <p>
                                Student Management
                            </p>
                        </a>
                    </li>
                @endcan
                @can('read documentCategory')
                    <li class="nav-item">
                        <a href="{{ route('documentCategory.index') }}"
                            class="nav-link {{ request()->is('documentCategory*') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-file-signature unique-doc-icon"></i>
                            <p>
                                Documents Category
                            </p>
                        </a>
                    </li>
                @endcan
                @can('read document')
                    <li class="nav-item">
                        <a href="{{ route('documents.index') }}"
                            class="nav-link {{ request()->is('documents*') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-file-signature unique-doc-icon"></i>
                            <p>
                                Documents
                            </p>
                        </a>
                    </li>
                @endcan
                <h3 class="main-title">Work Management</h3>
                @canany(['read classWork'])
                    <li class="nav-item">
                        <a href="{{ route('classWork.index') }}"
                            class="nav-link {{ request()->is('classWork*') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-chalkboard-teacher"></i>
                            <p>
                                ClassWork
                            </p>
                        </a>
                    </li>
                @endcan
                @canany(['read homeWork'])
                    <li class="nav-item">
                        <a href="{{ route('homeWork.index') }}"
                            class="nav-link {{ request()->is('homeWork*') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-pencil-alt"> </i>
                            <p>
                                HomeWork
                            </p>
                        </a>
                    </li>
                @endcan
                <li class="nav-item">
                        <a href="{{ route('studentleave.index') }}"
                            class="nav-link {{ request()->is('studentleave') ? 'active' : '' }}">
                            <i class="nav-icon fa fa-thumbs-up"></i>
                            <p>
                                Student Leaves
                            </p>
                        </a>
                    </li>
                @php
                    $classrooms = isClassTeacher();
                @endphp
                @if ($classrooms->count() > 0)
                    <li class="nav-item">
                        <a href="{{ route('student-attendance') }}"
                            class="nav-link {{ request()->is('student-attendance') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-chalkboard"></i>
                            <p>
                                Student Attendance
                            </p>
                        </a>
                    </li>
                @endif

                <h3 class="main-title">Finance</h3>
                @canany(['manage student fees', 'manage enquiry fees', 'manage passbook'])
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="nav-icon fa fa-file-invoice-dollar"></i>
                            <p>
                                Fees Collection
                                <i class="right fas fa-angle-left"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            @can('manage student fees')
                                <li class="nav-item">
                                    <a href="{{ route('getAllStudentPaymentLogs') }}"
                                        class="nav-link {{ request()->is('student-payment-data') ? 'active' : '' }}">
                                        <i class="nav-icon fa fa-rupee-sign"></i>
                                        <p>
                                            Fees Payments
                                        </p>
                                    </a>
                                </li>
                            @endcan
                            @can('manage enquiry fees')
                                <li class="nav-item">
                                    <a href="{{ route('enquiry.payments') }}"
                                        class="nav-link {{ request()->is('enquiry-payments') ? 'active' : '' }}">
                                        <i class="nav-icon fa fa-question-circle"></i>
                                        <p>
                                            Enquiry Payments
                                        </p>
                                    </a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endcanany
                @can('manage passbook')
                    <li class="nav-item">
                        <a href="{{ route('passbook') }}"
                            class="nav-link {{ request()->is('passbook') ? 'active' : '' }}">
                            <i class="nav-icon fa fa-file-invoice"></i>
                            <p>
                                Passbook
                            </p>
                        </a>
                    </li>
                @endcan
                <h3 class="main-title">Others</h3>
                @canany(['read circular'])
                    <li class="nav-item">
                        <a href="{{ route('circulars.index') }}"
                            class="nav-link {{ request()->is('circulars*') ? 'active' : '' }}">
                            <i class="nav-icon fas fa-newspaper"></i>
                            <p>
                                Circulars
                            </p>
                        </a>
                    </li>
                @endcan
                <h3 class="main-title">Insights & Stories</h3>
                <li class="nav-item">
                    <a href="{{ route('thoughts.index') }}"
                        class="nav-link {{ request()->is('thoughts') ? 'active' : '' }}">
                        <i class="nav-icon fa fa-lightbulb"></i>
                        <p>Thoughts</p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('testimonial.index') }}"
                        class="nav-link {{ request()->is('testimonial') ? 'active' : '' }}">
                        <i class="nav-icon fa fa-comment-dots"></i>
                        <p>Testimonials</p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('blogs.index') }}"
                        class="nav-link {{ request()->is('blogs') ? 'active' : '' }}">
                        <i class="nav-icon fa fa-blog"></i>
                        <p>Blogs</p>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="{{ route('profileviews') }}"
                        class="nav-link {{ request()->is('profileviews') ? 'active' : '' }}">
                        <i class="nav-icon fa fa-eye"></i>
                        <p>Profile Views</p>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</aside>
@include('layouts.searchModules')
