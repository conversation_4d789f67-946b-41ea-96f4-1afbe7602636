<?php
return [
    'STATUS' => [
        "PENDING" => 'PENDING',
        "ACTIVE" => 'ACTIVE',
        "INACTIVE" => 'INACTIVE',
        "BLOCK" => 'BLOCK',
        "REJECT" => 'REJECT',
        "APPROVED" => 'APPROVED',
        "VISITED" => 'VISITED',
        "EXPIRED" => 'EXPIRED',
        "OPEN" => 'OPEN',
        "CLOSED" => 'CLOSED',
        "ADMITTED" => 'ADMITTED',
        "FOllOWUP" => 'FOllOWUP',
        "SHORTLISTED" => 'SHORTLISTED',

    ],
    'VISIT_REASON' => [
        "ADMISSION" => 'ADMISSION',
        "INTERVIEW" => 'INTERVIEW',
        "PARENTS" => 'PARENTS',
        "OFFICE" => 'OFFICE',
    ],
    'PAYMENT_MODE' => [
        "ONLINE" => 'ONLINE',
        "CASH" => 'CASH',
        "CHEQUE" => 'CHEQUE',
    ],
    'PAYMENT_STATUS' => [
        "SUCCESS" => 'SUCCESS',
        "FAILED" => 'FAILED',
    ],
    'FEE_DURATION_LABELS' => [
        'PER_MONTH' => 'PER_MONTH',
        'PER_QUARTER' => 'PER_QUARTER',
        'PER_6_MONTH' => 'PER_6_MONTH',
        'PER_YEAR' => 'PER_YEAR',
        'CUSTOM' => 'CUSTOM'
    ],
    "PERMISSIONS" => [
        'DEPARTMENT_MANAGEMENT' => [
            'read department' => 'View',
            'create department' => 'Create',
            'update department' => 'Edit',
            'delete department' => 'Delete',
            'export department data' => 'Export',
        ],
        'ROLE_MANAGEMENT' => [
            'read role' => 'View',
            'create role' => 'Create',
            'update role' => 'Edit',
            'delete role' => 'Delete',
            'export role data' => 'Export',
        ],
        'USER_MANAGEMENT' => [
            'read users' => 'View',
            'create users' => 'Create',
            'update users' => 'Edit',
            'delete users' => 'Delete',
            'export users data' => 'Export',
        ],

        'CIRCULAR_MANAGEMENT' => [
            'create circular' => 'Create',
            'read circular' => 'View',
            'delete circular' => 'delete',
        ],
        'DOCUMENT_MANAGEMENT' => [
            'create document' => 'Create',
            'read document' => 'View',
            'delete document' => 'delete',
        ],
        'LEAVE_TYPE' => [
            'create leave type' => 'Create',
            'read leave type' => 'View',
            'update leave type' => 'update',
            'delete leave type' => 'delete',
            'export leave type' => 'export',
        ],
        'LEAVE_APPROVAL' => [
            'second leave approval' => 'Second leave approval',
            'export leave data' => 'Export Leave',
        ],
        'LEAVE_BALANCE' => [
            'create leave balance' => 'create',
            'read leave balance' => 'View',
            'update leave balance' => 'update',
            'delete leave balance' => 'delete',
            'export leave balance data' => 'Export',
        ],
        'EVENT_MANAGEMENT' => [
            'create event' => 'create',
            'read event' => 'View',
            'update event' => 'update',
            'delete event' => 'delete',
            'export event data' => 'export',
        ],
        'EXAM_MANAGEMENT' => [
            'create exam' => 'create',
            'read exam' => 'View',
            'update exam' => 'update',
            'delete exam' => 'delete',
            'download result'=>'download'
        ],
        'Exam_Paper'=>[
            'create exam paper'=>'create',
            'read exam paper'=>'View',
            'update exam paper'=>'update',
            'delete exam paper'=>'delete',
            'download hallticket'=>'download'
        ],
        'MARKS_MANAGEMENT'=>[
            'enter marks'=>'create',
            'view marks'=>'View',
            'update marks'=>'update',
            'lock marks'=>'lock',
            'download result'=>'download'
        ],

        'HOLIDAY_MANAGEMENT' => [
            'create holiday' => 'create',
            'read holiday' => 'View',
            'update holiday' => 'update',
            'delete holiday' => 'delete',
            'export holiday data' => 'export',
        ],
        'SALARY_INCREMENT' => [
            'salary increment' => 'Manage salary increment',
            'export salary increment data' => 'Export Salary increment',
        ],
        'SALARY_SLIP_MANAGEMENT' => [
            'generate salary slip' => 'Manage salary slips',
            'export salary data' => 'Export Salaries (Bank & detailed)',
        ],
        'CLASSROOM_MANAGEMENT' => [
            'create classroom' => 'create',
            'read classroom' => 'View',
            'update classroom' => 'update',
            'delete classroom' => 'delete',
            'manage classroom fees' => 'manage fees',
            'export classroom data' => 'Export',
        ],

        'SUBJECT_MANAGEMENT' => [
            'create subject' => 'create',
            'read subject' => 'View',
            'update subject' => 'update',
            'delete subject' => 'delete',
            'export subject data' => 'Export',
            'transfer subject data' => 'Transfer',
        ],

        'TIMESLOT_MANAGEMENT' => [
            'create timeslot' => 'create',
            'read timeslot' => 'View',
            'update timeslot' => 'update',
            'delete timeslot' => 'delete',
            'export timeslot data' => 'Export',
        ],

        'RESOURCE_MANAGEMENT' => [
            'create resource' => 'create',
            'read resource' => 'View',
            'update resource' => 'update',
            'delete resource' => 'delete',
            'export resource data' => 'Export',
        ],

        'YEAR_MANAGEMENT' => [
            'create year' => 'create',
            'read year' => 'View',
            'update year' => 'update',
            'delete year' => 'delete',
        ],

        'VEHICLE_MANAGEMENT' => [
            'create vehicle' => 'create',
            'read vehicle' => 'View',
            'update vehicle' => 'update',
            'delete vehicle' => 'delete',
            'export vehicle data' => 'Export',
        ],

        'ROUTE_MANAGEMENT' => [
            'create route' => 'create',
            'read route' => 'View',
            'update route' => 'update',
            'delete route' => 'delete',
            'export route data' => 'Export',
        ],

        'WAYPOINT_MANAGEMENT' => [
            'create waypoint' => 'create',
            'read waypoint' => 'View',
            'update waypoint' => 'update',
            'delete waypoint' => 'delete',
            'manage waypoint fees' => 'manage fees',
            'export waypoint data' => 'Export',
        ],

        'ENQUIRY_MANAGEMENT' => [
            'create enquiry' => 'create',
            'read enquiry' => 'View',
            'update enquiry' => 'update',
            'delete enquiry' => 'delete',
            'manage enquiry followups' => 'manage followups',
            'export enquiry data' => 'Export',
            'manage enquiry fees' => 'manage Fees',
            'export enquiry Fees' => 'Export Fees',
        ],

        'STUDENT_MANAGEMENT' => [
            'create student' => 'create',
            'read student' => 'View',
            'update student' => 'update',
            'manage student exit' => 'manage exit',
            'export student data' => 'Export',
        ],

        'STUDENT_FEES' => [
            'manage student fees' => 'manage fees',
            'export student fee data' => 'Export Fees',
        ],

        'FEES_CATEGORY_MANAGEMENT' => [
            'create fees category' => 'create',
            'read fees category' => 'View',
            'update fees category' => 'update',
            'delete fees category' => 'delete',
        ],

        'TIMETABLE_MANAGEMENT' => [
            'manage timetable' => 'Manage Timetable',
        ],
        'PASSBOOK' => [
            'manage passbook' => 'view',
            'export passbook data' => 'Export',
        ],

        'EXPENSE_TYPE' => [
            'create expenseType' => 'create',
            'read expenseType' => 'View',
            'update expenseType' => 'update',
            'delete expenseType' => 'delete',
            'export expenseType data' => 'export',
        ],
        'EXPENSE_SUPPLIER' => [
            'create expenseSupplier' => 'create',
            'read expenseSupplier' => 'View',
            'update expenseSupplier' => 'update',
            'delete expenseSupplier' => 'delete',
            'export expenseSupplier data' => 'export',
        ],
        'EXPENSE_MANAGEMENT' => [
            'create expense' => 'create',
            'read expense' => 'View',
            'update expense' => 'update',
            'delete expense' => 'delete',
            'export expense data' => 'export',
        ],
        'GATEPASS_MANAGEMENT' => [
            'create gatepass' => 'create',
            'read gatepass' => 'View',
            'update gatepass' => 'update',
            'delete gatepass' => 'delete',
            'scan gatepass' => 'scan',
        ],
        'DOCUMENTCATEGORY_MANAGEMENT' => [
            'create documentCategory' => 'create',
            'read documentCategory' => 'View',
            'update documentCategory' => 'update',
            'delete documentCategory' => 'delete',
            'export documentCategory data' => 'export',
        ],
    ],
];
