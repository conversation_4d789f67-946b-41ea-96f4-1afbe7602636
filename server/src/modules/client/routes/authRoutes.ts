import { Router } from 'express'
import { loginUser, logout, registerUser, resendVerificationEmail, verifyEmail,verifyOtpController,resendOtp, continueWithEmail, generateJWT } from '../controllers/authController'
import validateRequest from '@/middlewares/validateRequest'
import { continueWithEmailSchema, loginSchema, registerSchema,resendOtpSchema,verifyOtpSchema } from '../requests/authRequest'
import { authClientMiddleware } from '@/middlewares/clientAuth';

const authClientRouter = Router();

authClientRouter.post('/register', validateRequest(registerSchema), registerUser);
authClientRouter.post('/continue-with-email', validateRequest(continueWithEmailSchema), continueWithEmail);
authClientRouter.post('/verify-otp', validateRequest(verifyOtpSchema), verifyOtpController);
authClientRouter.post('/resend-otp', validateRequest(resendOtpSchema), resendOtp);
authClientRouter.post('/login', validateRequest(loginSchema), loginUser);
authClientRouter.get('/verify-email', verifyEmail)
authClientRouter.post('/resend-verification', resendVerificationEmail)
authClientRouter.post('/logout', logout);
authClientRouter.post('/generate-jwt', authClientMiddleware, generateJWT);

export default authClientRouter;
