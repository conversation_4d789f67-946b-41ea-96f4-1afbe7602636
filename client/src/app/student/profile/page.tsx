'use client';

import React, { useState, useRef, useEffect, Suspense } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, FileText, X, Camera, Upload, Check } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { fetchStudentProfile, updateStudentProfile } from '@/store/thunks/studentProfileThunks';
import { updateProfilePhoto } from '@/store/slices/studentProfileSlice';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import Header from '../../../app-components/Header';
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import Footer from "@/app-components/Footer";
import { SidebarNav } from "./components/sidebar-nav";

const profileFormSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters.'),
  middleName: z.string().optional(),
  lastName: z.string().min(2, 'Last name must be at least 2 characters.'),
  mothersName: z.string().optional(),
  email: z.string().email('Please enter a valid email address.').optional().or(z.literal('')),
  contact: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.'),
  contact2: z
    .string()
    .min(10, 'Contact number must be at least 10 digits.')
    .max(15, 'Contact number must not exceed 15 digits.')
    .regex(/^\d+$/, 'Contact number must contain only digits.')
    .optional()
    .or(z.literal('')),
  medium: z.string().min(1, 'Medium of instruction is required'),
  classroom: z.string().min(1, 'Standard is required'),
  gender: z.string().optional(),
  birthday: z.date({ required_error: 'Please select your date of birth' }),
  school: z.string().min(2, 'School name must be at least 2 characters.'),
  address: z.string().min(5, 'Address must be at least 5 characters.'),
  age: z.string().optional(),
  aadhaarNumber: z
    .string()
    .min(12, 'Aadhaar number must be 12 digits.')
    .max(12, 'Aadhaar number must be 12 digits.')
    .regex(/^\d+$/, 'Aadhaar number must contain only digits.')
    .optional()
    .or(z.literal('')),
  bloodGroup: z.string().optional(),
  birthPlace: z.string().optional(),
  motherTongue: z.string().optional(),
  religion: z.string().optional(),
  caste: z.string().optional(),
  subCaste: z.string().optional(),
  photo: z.any().optional(),
  document: z.any().optional(),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

const sidebarNavItems = [
  {
    title: "Personal Info",
    href: "#personal-info",
  },
  {
    title: "Educational Info",
    href: "#educational-info",
  },
  {
    title: "Documents & Photo",
    href: "#documents-photo",
  },
];

const StudentProfileContent = () => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams();
  const fromQuiz = searchParams.get('quiz') === 'true';
  const examId = searchParams.get('examId');

  const [activeSection, setActiveSection] = useState('personal-info');
  const [photo, setPhoto] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  const [uploadedDocument, setUploadedDocument] = useState<
    File | { name: string; size: number; url: string; type: string } | null
  >(null);
  const [isDocumentRemoved, setIsDocumentRemoved] = useState(false);
  const [progress, setProgress] = useState(0);

  const { profileData, loading: profileLoading } = useSelector(
    (state: RootState) => state.studentProfile
  );

  const profile = profileData?.profile || null;
  const classroomOptions = profileData?.classroomOptions || [];

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: '',
      middleName: '',
      lastName: '',
      mothersName: '',
      email: '',
      contact: '',
      contact2: '',
      medium: '',
      classroom: '',
      gender: '',
      birthday: undefined,
      school: '',
      address: '',
      age: '',
      aadhaarNumber: '',
      bloodGroup: '',
      birthPlace: '',
      motherTongue: '',
      religion: '',
      caste: '',
      subCaste: '',
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    const studentToken = localStorage.getItem('studentToken');
    if (!studentToken) {
      toast.error('Please login to access your profile');
      router.push('/');
    }
  }, [router]);

  useEffect(() => {
    const studentToken = localStorage.getItem('studentToken');
    if (studentToken) {
      dispatch(fetchStudentProfile());
    }
  }, [dispatch]);

  useEffect(() => {
    if (profileData?.profile) {
      const profile = profileData.profile;
      let completedSections = 0;
      const totalSections = 3;

      if (profile.student?.firstName && profile.student?.lastName && profile.student?.contact &&
          profile.birthday && profile.school && profile.address) {
        completedSections++;
      }

      if (profile.medium && profile.classroom) {
        completedSections++;
      }

      if (profile.photo && profile.documentUrl) {
        completedSections++;
      }

      setProgress((completedSections / totalSections) * 100);
    }
  }, [profileData]);

  useEffect(() => {
    if (!profileData) return;

    const profileObj = profileData.profile;
    const studentData = profileObj?.student || JSON.parse(localStorage.getItem('student_data') || '{}');

    const formValues = {
      firstName: studentData?.firstName || '',
      middleName: studentData?.middleName || '',
      lastName: studentData?.lastName || '',
      mothersName: studentData?.mothersName || '',
      email: studentData?.email || '',
      contact: studentData?.contact || '',
      contact2: profileObj?.contactNo2 || '',
      medium: profileObj?.medium || '',
      classroom: profileObj?.classroom || '',
      gender: profileObj?.gender || '',
      birthday: profileObj?.birthday ? new Date(profileObj.birthday) : undefined,
      school: profileObj?.school || '',
      address: profileObj?.address || '',
      age: profileObj?.age?.toString() || '',
      aadhaarNumber: profileObj?.aadhaarNo || '',
      bloodGroup: profileObj?.bloodGroup || '',
      birthPlace: profileObj?.birthPlace || '',
      motherTongue: profileObj?.motherTongue || '',
      religion: profileObj?.religion || '',
      caste: profileObj?.caste || '',
      subCaste: profileObj?.subCaste || '',
    };

    if (profileObj?.photo && !photo) {
      setPhoto(profileObj.photo);
      form.setValue('photo', profileObj.photo);
    }

    if (profileObj?.documentUrl && !uploadedDocument && !isDocumentRemoved) {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';
      const documentUrl = profileObj.documentUrl.startsWith('http')
        ? profileObj.documentUrl
        : `${baseUrl}${profileObj.documentUrl}`;

      const documentObj = {
        name: documentUrl.split('/').pop() || 'Uploaded Document',
        size: 0,
        url: documentUrl,
        type: 'application/octet-stream',
      };

      setUploadedDocument(documentObj);
      form.setValue('document', documentObj);
    }

    const currentValues = form.getValues();
    const isFormEmpty = !currentValues.firstName && !currentValues.lastName && !currentValues.contact;
    const isEducationalDataMissing = !currentValues.medium || !currentValues.classroom;

    if (isFormEmpty || isEducationalDataMissing) {
      form.reset(formValues);
    }
  }, [profileData, form, photo, uploadedDocument, isDocumentRemoved]);

  const openCamera = async () => {
    setCameraError(null);

    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        throw new Error('Camera not supported on this device');
      }

      setIsCameraOpen(true);

      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.onloadedmetadata = () => {
          videoRef.current?.play().catch(() => toast.error('Error starting camera preview'));
        };
      }
    } catch (error: any) {
      setIsCameraOpen(false);
      const message = error.name === 'NotAllowedError'
        ? 'Please allow camera access in your browser settings.'
        : 'Could not access camera. Please check your camera settings.';
      setCameraError(message);
      toast.error(message);
    }
  };

  const compressImage = (canvas: HTMLCanvasElement, maxWidth: number = 800, quality: number = 0.6): string => {
    const context = canvas.getContext('2d');
    if (!context) return '';

    const originalWidth = canvas.width;
    const originalHeight = canvas.height;

    let newWidth = originalWidth;
    let newHeight = originalHeight;

    if (originalWidth > maxWidth) {
      newWidth = maxWidth;
      newHeight = (originalHeight * maxWidth) / originalWidth;
    }

    const compressedCanvas = document.createElement('canvas');
    compressedCanvas.width = newWidth;
    compressedCanvas.height = newHeight;

    const compressedContext = compressedCanvas.getContext('2d');
    if (!compressedContext) return '';

    compressedContext.drawImage(canvas, 0, 0, newWidth, newHeight);

    return compressedCanvas.toDataURL('image/jpeg', quality);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    context?.clearRect(0, 0, canvas.width, canvas.height);
    context?.save();
    context?.scale(-1, 1);
    context?.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
    context?.restore();

    const compressedPhotoDataUrl = compressImage(canvas, 800, 0.6);
    const base64Data = compressedPhotoDataUrl.split(',')[1];
    const sizeInKB = (base64Data.length * 3) / 4 / 1024;

    if (sizeInKB > 5120) {
      toast.error('Photo size exceeds 5MB limit. Please try again.');
      return;
    }

    setPhoto(compressedPhotoDataUrl);
    form.setValue('photo', compressedPhotoDataUrl);
    dispatch(updateProfilePhoto(compressedPhotoDataUrl));

    closeCamera();
  };

  const closeCamera = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsCameraOpen(false);
    setCameraError(null);
  };

  const removeDocument = () => {
    if (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:')) {
      URL.revokeObjectURL(uploadedDocument.url);
    }

    setUploadedDocument(null);
    setIsDocumentRemoved(true);
    const fileInput = document.getElementById('document') as HTMLInputElement;
    if (fileInput) fileInput.value = '';
    form.setValue('document', null);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };



  const onSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);

    try {
      const currentPhoto = photo || profileData?.profile?.photo;
      if (!currentPhoto) {
        toast.error('Please capture a photo for your profile');
        setIsSubmitting(false);
        return;
      }

      if (!uploadedDocument || isDocumentRemoved) {
        toast.error('Identity document is required. Please upload a document.');
        setIsSubmitting(false);
        return;
      }

      if (!(await form.trigger())) {
        toast.error('Please fill in all required fields correctly');
        setIsSubmitting(false);
        return;
      }

      const jsonData: any = {
        firstName: data.firstName,
        middleName: data.middleName,
        lastName: data.lastName,
        mothersName: data.mothersName,
        email: data.email,
        contact: data.contact,
        contact2: data.contact2,
        medium: data.medium,
        classroom: data.classroom,
        gender: data.gender,
        birthday: data.birthday?.toISOString() || '',
        school: data.school,
        address: data.address,
        age: data.age,
        aadhaarNumber: data.aadhaarNumber,
        bloodGroup: data.bloodGroup,
        birthPlace: data.birthPlace,
        motherTongue: data.motherTongue,
        religion: data.religion,
        caste: data.caste,
        subCaste: data.subCaste,
      };

      if (photo?.startsWith('data:')) {
        const base64Data = photo.split(',')[1];
        const sizeInKB = (base64Data.length * 3) / 4 / 1024;

        if (sizeInKB > 5120) {
          toast.error('Photo size exceeds 5MB limit.');
          return;
        }

        jsonData.photo = base64Data;
        jsonData.photoMimeType = 'image/jpeg';
      }

      if (uploadedDocument instanceof File || (uploadedDocument && 'url' in uploadedDocument && uploadedDocument.url.startsWith('blob:'))) {
        const documentFile = uploadedDocument instanceof File
          ? uploadedDocument
          : await fetch(uploadedDocument.url)
              .then(res => res.blob())
              .then(blob => new File([blob], uploadedDocument.name, { type: uploadedDocument.type }));

        const documentBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve((reader.result as string).split(',')[1]);
          reader.onerror = reject;
          reader.readAsDataURL(documentFile);
        });

        const docSizeKB = (documentBase64.length * 3) / 4 / 1024;
        if (docSizeKB > 5120) {
          toast.error('Document size exceeds 5MB limit.');
          return;
        }

        jsonData.document = documentBase64;
        jsonData.documentMimeType = documentFile.type;
        jsonData.documentName = documentFile.name;
      }

      // Handle document removal
      if (isDocumentRemoved && profileData?.profile?.documentUrl) {
        jsonData.removeDocument = true;
      }

      const studentToken = localStorage.getItem('studentToken');
      if (!studentToken) {
        toast.error('Please login to submit your profile');
        router.push('/');
        return;
      }

      const result = await dispatch(updateStudentProfile(jsonData));

      if (result.meta.requestStatus === 'fulfilled') {
        toast.success(`Profile ${profile ? 'updated' : 'created'} successfully!`);

        const existingStudentData = JSON.parse(localStorage.getItem('student_data') || '{}');
        const studentData = {
          ...existingStudentData,
          id: existingStudentData.id || profileData?.profile?.student?.id || '',
          firstName: data.firstName,
          middleName: data.middleName,
          lastName: data.lastName,
          mothersName: data.mothersName,
          email: data.email || existingStudentData.email || profileData?.profile?.student?.email || '',
          contact: data.contact,
        };

        localStorage.setItem('student_data', JSON.stringify(studentData));
        setIsDocumentRemoved(false);
        await dispatch(fetchStudentProfile());

        if (fromQuiz) {
          if (examId) {
            router.push(`/uwhiz-exam/${examId}`);
          } else {
            router.push('/mock-test');
          }
        } else {
          router.push('/');
        }
      } else if (result.meta.requestStatus === 'rejected') {
        const errorMessage = result.payload as string;

        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
          toast.error('Your session has expired. Please login again.');
          localStorage.removeItem('studentToken');
          router.push('/');
        } else {
          toast.error(errorMessage || 'Failed to update profile');
        }
      }
    } catch {
      toast.error('Failed to submit profile information');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Header />
      <div className="space-y-6 p-10 pb-4 md:block">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">
            Student Profile
          </h2>
          <p className="text-muted-foreground">
            Complete your profile information. Your progress will be automatically saved as you complete each section.
          </p>
        </div>
        <Progress value={progress} className="h-2" />
        <p className="text-sm text-muted-foreground">
          {Math.round(progress)}% complete
        </p>

        <Separator className="my-6" />
        <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
          <aside className="-mx-4 lg:w-1/6 pb-12">
            <SidebarNav
              items={sidebarNavItems}
              activeSection={activeSection}
              setActiveSection={setActiveSection}
            />
          </aside>
          <div className="flex justify-center w-full">
            <div className="flex-1 lg:max-w-2xl pb-12">
              {profileLoading ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <svg
                    className="animate-spin h-10 w-10 text-black mb-4"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                  <p className="text-gray-600">Loading profile information...</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">
                      {activeSection === 'personal-info' && 'Personal Information'}
                      {activeSection === 'educational-info' && 'Educational Information'}
                      {activeSection === 'documents-photo' && 'Documents & Photo'}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {activeSection === 'personal-info' && 'Enter your basic personal details and contact information'}
                      {activeSection === 'educational-info' && 'Select your medium of instruction and classroom standard'}
                      {activeSection === 'documents-photo' && 'Upload your photo and required documents'}
                    </p>
                  </div>
                  <Separator />

                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">

                    {/* Personal Information Section */}
                    {activeSection === 'personal-info' && (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="firstName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">First Name *</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter First Name"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="middleName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Middle Name</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Middle Name"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="lastName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Last Name *</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Last Name"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="mothersName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Mothers Name</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Mother's Name"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-black font-medium">Email</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                  placeholder="Enter Email"
                                  type="email"
                                />
                              </FormControl>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          )}
                        />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="contact"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Contact Number *</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="8520369851"
                                    type="tel"
                                    inputMode="numeric"
                                    pattern="[0-9]*"
                                    onKeyDown={(e) => {
                                      const specialKeys = [
                                        'Backspace',
                                        'Tab',
                                        'Enter',
                                        'Escape',
                                        'Delete',
                                        'ArrowLeft',
                                        'ArrowRight',
                                        'Home',
                                        'End',
                                      ];
                                      if (specialKeys.includes(e.key)) {
                                        return;
                                      }
                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
                                        return;
                                      }
                                      if (!/^\d$/.test(e.key)) {
                                        e.preventDefault();
                                      }
                                    }}
                                    onChange={(e) => {
                                      const value = e.target.value.replace(/\D/g, '');
                                      field.onChange(value);
                                    }}
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="contact2"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Contact Number 2</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Alternate Number"
                                    type="tel"
                                    inputMode="numeric"
                                    pattern="[0-9]*"
                                    onKeyDown={(e) => {
                                      const specialKeys = [
                                        'Backspace',
                                        'Tab',
                                        'Enter',
                                        'Escape',
                                        'Delete',
                                        'ArrowLeft',
                                        'ArrowRight',
                                        'Home',
                                        'End',
                                      ];
                                      if (specialKeys.includes(e.key)) {
                                        return;
                                      }
                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
                                        return;
                                      }
                                      if (!/^\d$/.test(e.key)) {
                                        e.preventDefault();
                                      }
                                    }}
                                    onChange={(e) => {
                                      const value = e.target.value.replace(/\D/g, '');
                                      field.onChange(value);
                                    }}
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <FormField
                            control={form.control}
                            name="gender"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Gender</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value || undefined}
                                >
                                  <FormControl>
                                    <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                                      <SelectValue placeholder="Select" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                                    <SelectItem value="male">Male</SelectItem>
                                    <SelectItem value="female">Female</SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="age"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Age</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Age"
                                    type="number"
                                    min="1"
                                    max="100"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="birthday"
                            render={({ field }) => (
                              <FormItem className="flex flex-col">
                                <FormLabel className="text-black font-medium">Date of Birth</FormLabel>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        'w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-50 rounded-lg',
                                        !field.value && 'text-muted-foreground'
                                      )}
                                    >
                                      {field.value && field.value instanceof Date && !isNaN(field.value.getTime()) ? (
                                        format(field.value, 'PPP')
                                      ) : (
                                        <span>Select your birthday</span>
                                      )}
                                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0 bg-white border border-gray-300 shadow-lg" align="start">
                                  <div className="p-3 border-b border-gray-200">
                                    <div className="flex gap-2 mb-3">
                                      <Select
                                        value={field.value ? field.value.getFullYear().toString() : ""}
                                        onValueChange={(year) => {
                                          const currentDate = field.value || new Date();
                                          const newDate = new Date(currentDate);
                                          newDate.setFullYear(parseInt(year));
                                          field.onChange(newDate);
                                        }}
                                      >
                                        <SelectTrigger className="w-24">
                                          <SelectValue placeholder="Year" />
                                        </SelectTrigger>
                                        <SelectContent className="max-h-48">
                                          {Array.from({ length: 125 }, (_, i) => {
                                            const year = new Date().getFullYear() - i;
                                            return (
                                              <SelectItem key={year} value={year.toString()}>
                                                {year}
                                              </SelectItem>
                                            );
                                          })}
                                        </SelectContent>
                                      </Select>
                                      <Select
                                        value={field.value ? field.value.getMonth().toString() : ""}
                                        onValueChange={(month) => {
                                          const currentDate = field.value || new Date();
                                          const newDate = new Date(currentDate);
                                          newDate.setMonth(parseInt(month));
                                          field.onChange(newDate);
                                        }}
                                      >
                                        <SelectTrigger className="w-32">
                                          <SelectValue placeholder="Month" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {[
                                            'January', 'February', 'March', 'April', 'May', 'June',
                                            'July', 'August', 'September', 'October', 'November', 'December'
                                          ].map((month, index) => (
                                            <SelectItem key={index} value={index.toString()}>
                                              {month}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                                    month={field.value || new Date()}
                                    className="rounded-md border-0"
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormDescription className="text-xs text-gray-500">
                                Your date of birth will be verified with your documents
                              </FormDescription>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          )}
                        />
                        </div>
                        <FormField
                          control={form.control}
                          name="address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-black font-medium">Address</FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  rows={3}
                                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg resize-none"
                                  placeholder="Enter your full address"
                                />
                              </FormControl>
                              <FormDescription className="text-xs text-gray-500">
                                Provide your complete residential address
                              </FormDescription>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="school"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-black font-medium">School Name *</FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                  placeholder="Enter School"
                                />
                              </FormControl>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          )}
                        />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="aadhaarNumber"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Aadhaar Number</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Aadhaar No"
                                    type="tel"
                                    inputMode="numeric"
                                    pattern="[0-9]*"
                                    maxLength={12}
                                    onKeyDown={(e) => {
                                      const specialKeys = [
                                        'Backspace',
                                        'Tab',
                                        'Enter',
                                        'Escape',
                                        'Delete',
                                        'ArrowLeft',
                                        'ArrowRight',
                                        'Home',
                                        'End',
                                      ];
                                      if (specialKeys.includes(e.key)) {
                                        return;
                                      }
                                      if (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase())) {
                                        return;
                                      }
                                      if (!/^\d$/.test(e.key)) {
                                        e.preventDefault();
                                      }
                                    }}
                                    onChange={(e) => {
                                      const value = e.target.value.replace(/\D/g, '');
                                      field.onChange(value);
                                    }}
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="bloodGroup"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Blood Group</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value || undefined}
                                >
                                  <FormControl>
                                    <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                                      <SelectValue placeholder="Select" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                                    <SelectItem value="A+">A+</SelectItem>
                                    <SelectItem value="A-">A-</SelectItem>
                                    <SelectItem value="B+">B+</SelectItem>
                                    <SelectItem value="B-">B-</SelectItem>
                                    <SelectItem value="AB+">AB+</SelectItem>
                                    <SelectItem value="AB-">AB-</SelectItem>
                                    <SelectItem value="O+">O+</SelectItem>
                                    <SelectItem value="O-">O-</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="birthPlace"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Birth Place</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Birth Place"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="motherTongue"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Mother Tongue</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Mother Tongue"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="religion"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Religion</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Religion"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="caste"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Caste</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Caste"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="subCaste"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Sub Caste</FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    className="bg-white border-gray-200 focus:ring-black focus:border-black rounded-lg"
                                    placeholder="Enter Sub Caste"
                                  />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    )}

                    {/* Educational Information Section */}
                    {activeSection === 'educational-info' && (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="medium"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Medium *</FormLabel>
                                <Select
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                  }}
                                  value={field.value || undefined}
                                >
                                  <FormControl>
                                    <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                                      <SelectValue placeholder="Select" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                                    <SelectItem value="english">English</SelectItem>
                                    <SelectItem value="gujarati">Gujarati</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="classroom"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-black font-medium">Standard *</FormLabel>
                                <Select
                                  onValueChange={(value) => {
                                    field.onChange(value);
                                  }}
                                  value={field.value || undefined}
                                >
                                  <FormControl>
                                    <SelectTrigger className="bg-white border-gray-300 focus:ring-black focus:border-black rounded-lg w-full">
                                      <SelectValue placeholder="Select" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent className="bg-white w-[var(--radix-select-trigger-width)]">
                                    {profileLoading ? (
                                      <div className="flex items-center justify-center p-4">
                                        <svg
                                          className="animate-spin h-5 w-5 text-black"
                                          xmlns="http://www.w3.org/2000/svg"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                        >
                                          <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                          />
                                          <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                          />
                                        </svg>
                                      </div>
                                    ) : classroomOptions.length > 0 ? (
                                      classroomOptions.map((option) => (
                                        <SelectItem key={option.id} value={option.value}>
                                          {option.value}
                                        </SelectItem>
                                      ))
                                    ) : (
                                      <div className="p-2 text-center text-gray-500">No classroom options available</div>
                                    )}
                                  </SelectContent>
                                </Select>
                                <FormMessage className="text-red-500" />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    )}

                    {/* Documents & Photo Section */}
                    {activeSection === 'documents-photo' && (
                      <div className="space-y-6">

                        <FormField
                          control={form.control}
                          name="photo"
                          render={() => (
                            <Card className="shadow-lg border-0">
                              <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
                                <CardTitle className="text-lg font-medium text-gray-800">Student Image</CardTitle>
                                <CardDescription className="text-gray-600">
                                  Take a clear photo of your face for your profile (Only jpg, jpeg, png allowed - MAX. 5MB)
                                </CardDescription>
                              </CardHeader>
                          <CardContent>
                            <FormItem>
                              <FormControl>
                                <div>
                                  {cameraError && (
                                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                                      <p className="text-red-700 text-sm">{cameraError}</p>
                                    </div>
                                  )}
                                  {!isCameraOpen && !photo && (
                                    <Button
                                      type="button"
                                      onClick={openCamera}
                                      className="w-full bg-black text-white font-medium py-6 rounded-lg flex items-center justify-center gap-2"
                                    >
                                      <Camera className="h-5 w-5 mr-2" />
                                      Open Camera
                                    </Button>
                                  )}
                                  {isCameraOpen && (
                                    <div className="camera-container border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                                      <video
                                        ref={videoRef}
                                        autoPlay
                                        playsInline
                                        className="w-full h-auto transform scale-x-[-1]"
                                      />
                                      <div className="flex p-4 bg-gray-50">
                                        <Button
                                          type="button"
                                          onClick={capturePhoto}
                                          variant="default"
                                          className="flex-1 mr-2 bg-black hover:bg-gray-800 text-white"
                                        >
                                          <Check className="h-4 w-4 mr-2" />
                                          Capture
                                        </Button>
                                        <Button
                                          type="button"
                                          onClick={closeCamera}
                                          variant="outline"
                                          className="flex-1 border-gray-300"
                                        >
                                          <X className="h-4 w-4 mr-2" />
                                          Cancel
                                        </Button>
                                      </div>
                                    </div>
                                  )}
                                  {!isCameraOpen && (profileData?.profile?.photo || photo) && (
                                    <div className="flex flex-col sm:flex-row items-center gap-4">
                                      <div className="border rounded-lg shadow-md bg-gray-50 p-4 max-w-full">
                                        <div className="flex justify-center">
                                          {(() => {
                                            const displayPhoto = photo || profileData?.profile?.photo;
                                            if (displayPhoto) {
                                              return (
                                                <Image
                                                  src={
                                                    displayPhoto.startsWith('data:')
                                                      ? displayPhoto
                                                      : displayPhoto.startsWith('http')
                                                      ? displayPhoto
                                                      : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${displayPhoto}?t=${new Date().getTime()}`
                                                  }
                                                  alt="Student Photo"
                                                  height={1000}
                                                  width={1000}
                                                  className="max-w-full max-h-80 object-contain rounded-lg"
                                                  style={{ height: 'auto', width: 'auto' }}
                                                  unoptimized={displayPhoto.startsWith('data:')}
                                                />
                                              );
                                            }
                                            return (
                                              <div className="flex items-center justify-center h-32 w-48 bg-gray-100 rounded-lg">
                                                <Camera className="h-12 w-12 text-gray-400" />
                                              </div>
                                            );
                                          })()}
                                        </div>
                                      </div>
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          setPhoto(null);
                                          setCameraError(null);
                                          dispatch(updateProfilePhoto(undefined));
                                          form.setValue('photo', null);
                                          openCamera();
                                        }}
                                        variant="outline"
                                        className="border-gray-300"
                                      >
                                        <Camera className="h-4 w-4 mr-2" />
                                        Retake Photo
                                      </Button>
                                    </div>
                                  )}
                                  <canvas ref={canvasRef} style={{ display: 'none' }} />
                                </div>
                              </FormControl>
                              <FormDescription className="text-xs text-gray-500 mt-2">
                                A clear photo helps us identify you and personalize your profile
                              </FormDescription>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          </CardContent>
                        </Card>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="document"
                      render={({ field }) => (
                        <Card className="shadow-lg border-0">
                          <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-lg">
                            <CardTitle className="text-lg font-medium text-gray-800">Identity Document</CardTitle>
                            <CardDescription className="text-gray-600">
                              Upload Aadhar card, Bonafide certificate, Leaving certificate, ID card or any other document that contains your birthdate
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <FormItem>
                              {!uploadedDocument ? (
                                <FormControl>
                                  <div className="flex items-center justify-center w-full">
                                    <label className="flex flex-col items-center justify-center w-full h-36 border-2 border-gray-200 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                        <Upload className="w-10 h-10 mb-3 text-black" />
                                        <p className="mb-2 text-sm text-gray-700">
                                          <span className="font-semibold">Click to upload</span> or drag and drop
                                        </p>
                                        <p className="text-xs text-gray-500">PDF, PNG, JPG or JPEG (MAX. 5MB)</p>
                                      </div>
                                      <Input
                                        id="document"
                                        type="file"
                                        accept=".pdf,.jpg,.jpeg,.png"
                                        className="hidden"
                                        onChange={(e) => {
                                          const file = e.target.files?.[0];
                                          if (file) {
                                            if (file.size > 5 * 1024 * 1024) {
                                              toast.error('File size exceeds 5MB limit');
                                              return;
                                            }
                                            const documentWithUrl = {
                                              name: file.name,
                                              size: file.size,
                                              type: file.type,
                                              url: URL.createObjectURL(file),
                                            };
                                            setUploadedDocument(documentWithUrl);
                                            setIsDocumentRemoved(false);
                                            field.onChange(file);
                                          }
                                        }}
                                      />
                                    </label>
                                  </div>
                                </FormControl>
                              ) : (
                                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                      <div className="p-2 bg-[#fff8f3] rounded-full">
                                        <FileText className="h-5 w-5 text-black" />
                                      </div>
                                      <div>
                                        <p className="text-sm font-medium text-gray-700">{uploadedDocument.name}</p>
                                        <p className="text-xs text-gray-500">
                                          {uploadedDocument instanceof File
                                            ? formatFileSize(uploadedDocument.size)
                                            : 'Previously uploaded document'}
                                        </p>
                                      </div>
                                    </div>
                                    <div className="flex space-x-2">
                                      {uploadedDocument && 'url' in uploadedDocument && (
                                        <Button
                                          type="button"
                                          variant="outline"
                                          size="sm"
                                          onClick={() => window.open(uploadedDocument.url, '_blank')}
                                          className="h-8 px-3 border-gray-200"
                                        >
                                          View
                                        </Button>
                                      )}
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={removeDocument}
                                        className="h-8 w-8 p-0 border-gray-200"
                                      >
                                        <X className="h-4 w-4 text-gray-500" />
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              )}
                              <FormDescription className="text-xs text-gray-500 mt-2">
                                This document will serve to verify your identity and date of birth.
                              </FormDescription>
                              <FormMessage className="text-red-500" />
                            </FormItem>
                          </CardContent>
                        </Card>
                      )}
                    />
                      </div>
                    )}

                    {/* Submit Button */}
                    <div className="flex justify-end pt-6 border-t border-gray-100">
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-black text-white hover:bg-gray-800 disabled:bg-gray-300 disabled:cursor-not-allowed px-8 py-3 font-medium transition-all duration-200"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2">
                            <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                            </svg>
                            Saving...
                          </div>
                        ) : profileData ? (
                          'Update Profile'
                        ) : (
                          'Save Profile'
                        )}
                      </Button>
                    </div>

                    </form>
                  </Form>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

const StudentProfilePage = () => {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <svg
            className="animate-spin h-10 w-10 text-black"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </div>
      }
    >
      <StudentProfileContent />
    </Suspense>
  );
};

export default StudentProfilePage;