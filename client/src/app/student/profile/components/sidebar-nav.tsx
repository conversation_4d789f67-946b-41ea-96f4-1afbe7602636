import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { CheckCircle } from 'lucide-react';

interface SidebarNavProps {
  items: { title: string; href: string }[];
  activeSection: string;
  setActiveSection: (section: string) => void;
}

export function SidebarNav({ items, activeSection, setActiveSection }: SidebarNavProps) {
  const { profileData } = useSelector((state: RootState) => state.studentProfile);

  const isFormCompleted = (title: string) => {
    if (!profileData?.profile) return false;
    
    const profile = profileData.profile;
    
    switch (title) {
      case "Personal Info":
        return !!(profile.student?.firstName && profile.student?.lastName && 
                 profile.student?.contact && profile.birthday && profile.school && profile.address);
      case "Educational Info":
        return !!(profile.medium && profile.classroom);
      case "Documents & Photo":
        return !!(profile.photo && profile.documentUrl);
      default:
        return false;
    }
  };

  return (
    <nav className="space-y-1">
      {items.map((item, index) => {
        const isActive = activeSection === item.href.replace('#', '');
        const isCompleted = isFormCompleted(item.title);
        
        // Disable if previous form is not completed (except for first item)
        const isDisabled = index > 0 && !isFormCompleted(items[index - 1].title);

        return (
          <button
            key={item.href}
            onClick={() => !isDisabled && setActiveSection(item.href.replace('#', ''))}
            className={`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${
              isActive 
                ? 'bg-muted text-primary' 
                : isDisabled 
                ? 'text-gray-400 cursor-not-allowed' 
                : 'text-muted-foreground hover:text-primary'
            }`}
            disabled={isDisabled}
          >
            <span>{item.title}</span>
            {isCompleted && <CheckCircle size={16} className="text-green-500" />}
          </button>
        );
      })}
    </nav>
  );
}
