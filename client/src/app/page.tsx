"use client";

import React, { Suspense, useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Target,
  Clock,
  Rocket,
  Music,
  Brush,
  Film,
  Dumbbell,
  Languages,
  Laptop,
  School,
  Star,
  X,
  ArrowRight,
  Sparkles,
  Monitor,
  CookingPot,
  PartyPopper,
  Sigma,
  Activity,
  StretchHorizontal,
  Plane,
  PenTool,
  ArrowLeft,
} from "lucide-react";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import AuthErrorHandler from "@/app-components/AuthErrorHandler";
import { motion, MotionConfig } from "framer-motion";
// import Link from "next/link";
import Image from "next/image";
// import { useTheme } from "next-themes";
import { axiosInstance } from "@/lib/axios";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import ThoughtSlider from "./classes/components/thoughtSlider";
import { getThought, Thought } from "@/services/classesThoughtApi";
import TestimonialSlider from "@/app-components/TestimonialSlider";
import RecentBlogs from "@/app-components/RecentBlogs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import StatsSection from "@/app-components/StatsSection";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { Variants, Transition } from "framer-motion";
import Link from "next/link";

interface TuitionClass {
  education: string;
  coachingType: string;
  boardType?: string;
  medium?: string;
  section?: string;
  subject?: string;
  details?: string;
  pricingPerMonth: number;
  pricingPerCourse: number;
  timeSlots: { id: string; from: string; to: string }[];
}

interface Tutor {
  id: string;
  firstName: string;
  lastName: string;
  className: string;
  ClassAbout: {
    tutorBio: string;
    classesLogo: string;
  };
  tuitionClasses: TuitionClass[];
  averageRating?: number;
  reviewCount?: number;
}

const FirstPage = () => {
  const router = useRouter();
  const [categoryCounts, setCategoryCounts] = useState<Record<string, number>>(
    {}
  );
  const [approvedTutors, setApprovedTutors] = useState<Tutor[]>([]);
  const [thoughts, setThoughts] = useState<Thought[]>([]);
  const [loading, setLoading] = useState(true);
  const [thoughtsLoading, setThoughtsLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [categories, setCategories] = useState<Array<{ name: string; icon: React.ReactElement }>>([]);

  useEffect(() => {
    setOpen(true);
  }, []);

  const getIconForCategory = (categoryName: string) => {
    const iconMap: { [key: string]: React.ReactElement } = {
      "Education": <School className="w-10 h-10" />,
      "Drama": <Film className="w-10 h-10" />,
      "Music": <Music className="w-10 h-10" />,
      "Art & Craft": <Brush className="w-10 h-10" />,
      "Sports": <Dumbbell className="w-10 h-10" />,
      "Foreign Languages": <Languages className="w-10 h-10" />,
      "Technology": <Laptop className="w-10 h-10" />,
      "Dance": <Sparkles className="w-10 h-10" />,
      "Computer Classes": <Monitor className="w-10 h-10" />,
      "Cooking Classes": <CookingPot className="w-10 h-10" />,
      "Garba Classes": <PartyPopper className="w-10 h-10" />,
      "Vaidik Maths": <Sigma className="w-10 h-10" />,
      "Gymnastic Classes": <Activity className="w-10 h-10" />,
      "Yoga Classes": <StretchHorizontal className="w-10 h-10" />,
      "Aviation Classes": <Plane className="w-10 h-10" />,
      "Designing Classes": <PenTool className="w-10 h-10" />,
    };
    return iconMap[categoryName] || <School className="w-10 h-10" />;
  };

  // Fetch categories from database
  const fetchCategories = async () => {
    try {
      const response = await axiosInstance.get("/constant/TuitionClasses");

      if (response.data && response.data.details) {
        const dynamicCategories = response.data.details.map((detail: any) => ({
          name: detail.name,
          icon: getIconForCategory(detail.name)
        }));
        setCategories(dynamicCategories);
      }
    } catch (error) {
      console.error("Failed to fetch categories:", error);
      setCategories([]);
    } finally {
    }
  };


  // const { theme } = useTheme();
  const [totalTutors, setTotalTutors] = useState(0);
  const [totalStudent, setTotalStudent] = useState(0);

  useEffect(() => {
    const fetchCategoryCounts = async () => {
      try {
        const response = await axiosInstance.get("/classes/category-counts");
        setCategoryCounts(response.data);
      } catch (error) {
        console.error("Error fetching category counts:", error);
      }
    };

    const fetchTotalStudent = async () => {
      try {
        const response = await axiosInstance.get("/student/count");
        setTotalStudent(response.data || 0);
      } catch (error) {
        console.error("Error fetching total students:", error);
        setTotalStudent(0);
      }
    };

    fetchCategoryCounts();
    fetchTotalStudent();
  }, []);

  const handleCategoryClick = (categoryName: string) => {
    router.push(`/verified-classes?education=${categoryName}`);
  };

  const fetchTutors = async () => {
    setLoading(true);
    try {
      const res = await axiosInstance.get("/classes/approved-tutors", {
        params: {
          page: 1,
          limit: 4,
          sortByRating: true,
          sortByReviewCount: true,
        },
      });

      if (res.data && typeof res.data === 'object') {
        if (res.data.success !== undefined && res.data.data !== undefined) {
          const responseData = res.data.data;
          setTotalTutors(responseData.totalClasses || 0);
          setApprovedTutors(responseData.data || []);
        } else {
          setTotalTutors(res.data.totalClasses || 0);
          setApprovedTutors(res.data.data || []);
        }
      } else {
        setTotalTutors(0);
        setApprovedTutors([]);
      }
    } catch (error) {
      console.error("Failed to fetch tutors:", error);
      toast.error("Failed to fetch tutors");
      setTotalTutors(0);
      setApprovedTutors([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchThoughts = async () => {
    setThoughtsLoading(true);
    try {
      const response = await getThought("APPROVED", undefined, 1, 5);
      const approvedThoughts =
        response.thoughts?.filter((thought) => thought.status === "APPROVED") ||
        [];
      setThoughts(approvedThoughts);
    } catch (error: any) {
      console.error("Error fetching thoughts:", error);
      toast.error("Failed to fetch thoughts");
      setThoughts([]);
    } finally {
      setThoughtsLoading(false);
    }
  };

  useEffect(() => {
    fetchTutors();
    fetchThoughts();
    fetchCategories();
  }, []);


  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      } as Transition,
    },
  };

  const itemVariants: Variants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring" as const,
        stiffness: 100,
      } as Transition,
    },
  };

  return (
    <>

      <Header />
      <div className="min-h-screen bg-background text-foreground overflow-hidden">
        <Suspense>
          <AuthErrorHandler />
        </Suspense>

        <main className="relative">
          <section className="min-h-screen flex items-center justify-center">
            {/* <div className="w-[85%] max-sm:w-[95%] mx-auto m-2">
            <Swiper
              modules={[Autoplay, Pagination]}
              autoplay={{ delay: 4000, disableOnInteraction: false }}
              spaceBetween={0}
              slidesPerView={1}
              pagination={{
                clickable: true,
                bulletClass: "swiper-pagination-bullet",
                bulletActiveClass: "swiper-pagination-bullet-active",
                el: ".custom-pagination",
              }}
              className="w-full"
            >
              <SwiperSlide>
                <div className="w-full aspect-[2/1] max-sm:aspect-[3/2]">
                  <Image
                    src="/slide1.png"
                    alt="Slide 1"
                    fill
                    className="w-full h-auto object-contain"
                    priority
                    sizes="85vw"
                  />
                </div>
              </SwiperSlide>
              <SwiperSlide>
                <div className="w-full aspect-[2/1] max-sm:aspect-[3/2]">
                  <Image
                    src="/banner_maths_marvel1.png"
                    alt="Slide 2"
                    fill
                    className="w-full h-auto object-contain"
                    priority
                    sizes="85vw"
                  />
                </div>
              </SwiperSlide>
            </Swiper>
            <div className="custom-pagination text-center mt-4"></div>
          </div> */}

            <motion.div
              className="container mx-auto text-center relative z-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <motion.div
                className="inline-block mb-6 rounded-full bg-[#fff9f3] backdrop-blur-sm border border-border px-6 py-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <span className="text-[#FD904B] font-medium">Your Gateway to Educational Excellence</span>
              </motion.div>

              <motion.h1
                className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-foreground via-[#FD904B] to-foreground bg-clip-text text-transparent drop-shadow-sm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Lessons,
                <span className="text-[#FD904B] mx-2">you’ll love</span>
                Guaranteed.
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                Try another classes for free if you’re not satisfied.
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <Link href="/verified-classes">
                  <Button
                    size="lg"
                    variant="outline"
                    className="px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300"
                  >
                    Explore Now
                  </Button>
                </Link>
                <Link href="https://play.google.com/store/apps/details?id=com.uest">
                  <Button
                    size="lg"
                    variant="outline"
                    className="px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300"
                  >
                    <Image
                      src="/googlePlay.png"
                      alt="Google Play Store"
                      width={32}
                      height={32}
                      className="object-contain"
                    />
                    Download App
                    <ArrowRight className="w-5" />
                  </Button>
                </Link>
              </motion.div>

              <motion.div
                className="flex justify-center items-center gap-8 flex-wrap mt-20"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <div className="flex items-center gap-2">
                  <Image
                    src="/DPIIT-white.png"
                    height={200}
                    width={200}
                    alt="Startup India"
                  />
                  <span className="text-sm font-medium text-muted-foreground">
                    Backed by Startup India
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Image
                    src="/iso-white.png"
                    height={100}
                    width={200}
                    alt="ISO Certified"
                  />
                  <span className="text-sm font-medium text-muted-foreground">
                    ISO 9001:2015 Certified
                  </span>
                </div>
              </motion.div>
            </motion.div>

          </section>

          <StatsSection totalTutors={totalTutors} totalStudent={totalStudent} />

          <section className="py-20 relative">
            <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"></div>
            <div className="container mx-auto px-4 relative z-10">
              <motion.div
                className="text-center mb-16"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <span className="text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block">
                  Featured Classes
                </span>
                <h2 className="text-4xl font-bold bg-clip-text mb-4">
                  Meet Our Top Tutors
                </h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Connect with our top verified tutors and start your learning
                  journey today.
                </p>
              </motion.div>

              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[...Array(4)].map((_, i) => (
                    <div
                      key={i}
                      className="h-96 w-full rounded-xl bg-gray-200 dark:bg-gray-700 animate-pulse"
                    />
                  ))}
                </div>
              ) : approvedTutors.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-10"
                >
                  <p className="text-muted-foreground">
                    No tutors found at the moment.
                  </p>
                </motion.div>
              ) : (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
                >
                  {approvedTutors.map((tutor, i) => (
                    <motion.div
                      key={i}
                      variants={itemVariants}
                      whileHover={{ y: -5 }}
                      className="h-full"
                    >
                      <Card className="h-full bg-white/50 dark:bg-black/50 backdrop-blur-sm transition-all duration-300">
                        <CardHeader className="flex flex-row items-center gap-4">
                          <motion.div
                            className="relative w-20 h-20 rounded-full overflow-hidden ring-2 ring-[#FD904B]/20"
                            whileHover={{ scale: 1.05 }}
                          >
                            <Image
                              src={
                                tutor.ClassAbout && tutor.ClassAbout.classesLogo
                                  ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${tutor.ClassAbout.classesLogo}`
                                  : "/default-profile.jpg"
                              }
                              alt={tutor.firstName}
                              fill
                              className="object-cover"
                            />
                          </motion.div>
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold hover:text-[#FD904B] transition-colors">
                              {tutor.firstName} {tutor.lastName}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {tutor.className}
                            </p>
                          </div>
                        </CardHeader>
                        <CardContent className="flex-1 space-y-4">
                          <p className="line-clamp-2 text-sm text-muted-foreground">
                            {(tutor.ClassAbout && tutor.ClassAbout.tutorBio) ||
                              "No bio available."}
                          </p>
                        </CardContent>
                        <CardFooter className="flex flex-col items-start gap-4">
                          <div className="flex items-center gap-1 pt-2">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-semibold text-foreground">
                              {tutor.averageRating
                                ? tutor.averageRating.toFixed(1)
                                : "0"}
                            </span>
                            <span>({tutor.reviewCount || 0} reviews)</span>
                          </div>
                          <Button
                            className="w-full bg-orange-500 hover:bg-orange-600"
                            onClick={() =>
                              router.push(`/classes-details/${tutor.id}`)
                            }
                          >
                            View Profile
                          </Button>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          </section>

          <MotionConfig transition={{ duration: 0.4 }}>
            <section className="py-20 relative">
              <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"></div>
              <div className="container mx-auto px-4 relative z-10">

                <motion.div
                  className="text-center mb-12"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                >
                  <span className="text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block">
                    Categories
                  </span>
                  <h2 className="text-4xl font-bold bg-clip-text mb-4">Explore Your Interests</h2>
                  <p className="text-muted-foreground max-w-2xl mx-auto">
                    Discover classes across various categories with our verified tutors.
                  </p>
                </motion.div>

                <div className="flex justify-end items-center gap-4 mb-6">
                  <button className="swiper-button-prev-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition">
                    <ArrowLeft size={20} />
                  </button>
                  <button className="swiper-button-next-custom p-2 rounded-full bg-[#FD904B] text-white hover:bg-[#fd904b]/90 transition">
                    <ArrowRight size={20} />
                  </button>
                </div>

                <Swiper
                  modules={[Navigation, Autoplay]}
                  autoplay={{ delay: 3000, disableOnInteraction: false }}
                  navigation={{
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                  }}
                  spaceBetween={20}
                  breakpoints={{
                    320: { slidesPerView: 1.2 },
                    640: { slidesPerView: 2 },
                    1024: { slidesPerView: 3 },
                    1280: { slidesPerView: 4 },
                  }}
                  className="!px-2 !pt-3"
                >
                  {categories.map((category, i) => (
                    <SwiperSlide key={i}>
                      <motion.div
                        key={i}
                        className="group cursor-pointer"
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ delay: i * 0.1 }}
                        viewport={{ once: true }}
                        whileHover={{ y: -5, transition: { duration: 0.2 } }}
                        onClick={() => handleCategoryClick(category.name)}
                      >
                        <div className="relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B] transition-all duration-300 overflow-hidden shadow-sm group-hover:shadow-md">
                          <div className="absolute inset-0 bg-gradient-to-br from-[#FD904B]/0 to-transparent group-hover:from-[#FD904B]/10 rounded-2xl transition-all duration-300"></div>

                          {!loading && (
                            <div className="absolute top-3 right-3 z-10">
                              <span className="text-sm font-bold bg-[#FD904B] text-white px-3 py-1.5 rounded-full shadow-sm flex items-center justify-center min-w-[40px] transform transition-all duration-300 group-hover:scale-110">
                                {categoryCounts[category.name] || 0}
                                <span className="ml-1 text-xs hidden group-hover:inline">
                                  classes
                                </span>
                              </span>
                            </div>
                          )}

                          <div className="relative">
                            <div className="text-[#FD904B] mb-6 p-4 bg-[#FD904B]/10 rounded-full inline-flex transform group-hover:scale-110 transition-all duration-300 group-hover:shadow-md group-hover:bg-[#FD904B]/20">
                              {category.icon}
                            </div>
                            <div className="mb-3">
                              <h3 className="text-2xl font-bold text-foreground">
                                {category.name}
                              </h3>
                            </div>
                            <p className="text-muted-foreground group-hover:text-[#FD904B] transition-colors duration-300 flex items-center gap-1 font-medium">
                              Explore courses{" "}
                              <span className="transform transition-transform group-hover:translate-x-1">
                                →
                              </span>
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            </section>
          </MotionConfig>

          {(thoughts.length > 0 || thoughtsLoading) && (
            <section className="py-20 relative">
              <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"></div>
              <div className="container mx-auto px-4 relative z-10">
                <motion.div
                  className="text-center mb-16"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                >
                  <span className="text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block">
                    Thoughts
                  </span>
                  <h2 className="text-4xl font-bold bg-clip-text mb-4">
                    What Our Community Thinks
                  </h2>
                  <p className="text-muted-foreground max-w-2xl mx-auto">
                    Hear from our verified students and tutors about their
                    experiences.
                  </p>
                </motion.div>

                {thoughtsLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
                  </div>
                ) : (
                  <ThoughtSlider thoughts={thoughts} />
                )}
              </div>
            </section>
          )}

          {/* How It Works Section */}
          <section className="py-20 relative">
            <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"></div>
            <div className="container mx-auto px-4 relative z-10">
              <motion.div
                className="text-center mb-16"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <span className="text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block">
                  Process
                </span>
                <h2 className="text-4xl font-bold bg-clip-text">
                  How UEST Works
                </h2>
              </motion.div>

              <div className="grid md:grid-cols-3 gap-8">
                {[
                  {
                    icon: <Target className="w-8 h-8" />,
                    title: "Find Your Perfect Match",
                    description:
                      "Browse through our verified classes and find your ideal match",
                  },
                  {
                    icon: <Clock className="w-8 h-8" />,
                    title: "Schedule Lessons",
                    description:
                      "Book lessons at times that work best for your schedule",
                  },
                  {
                    icon: <Rocket className="w-8 h-8" />,
                    title: "Start Learning",
                    description:
                      "Begin your learning journey with personalized guidance",
                  },
                ].map((step, index) => (
                  <motion.div
                    key={index}
                    className="group relative"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2 }}
                    viewport={{ once: true }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-[#FD904B]/5 to-transparent rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div className="relative p-8 h-52 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border group-hover:border-[#FD904B]/50 transition-all duration-300 shadow-sm">
                      <div className="text-[#FD904B] mb-6 transform group-hover:scale-110 transition-transform duration-300">
                        {step.icon}
                      </div>
                      <h3 className="text-2xl font-semibold mb-4 text-foreground">
                        {step.title}
                      </h3>
                      <p className="text-muted-foreground">
                        {step.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
          <TestimonialSlider />

          {/* Recent Blogs Section */}
          <RecentBlogs />
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-xs p-0 overflow-hidden">
              <div className="sr-only">
                <DialogHeader>
                  <DialogTitle>Uwhiz Winner</DialogTitle>
                </DialogHeader>
              </div>
              <button
                onClick={() => setOpen(false)}
                className="absolute top-2 right-2 z-10 bg-white rounded-full p-1 shadow-md"
              >
                <X className="w-6 h-6" />
              </button>
              <Image
                src="/Uwhiz-super-kids-winner.png"
                alt="Uwhiz Winner"
                width={600}
                height={400}
                className="w-full h-full object-cover rounded-lg"
              />
            </DialogContent>
          </Dialog>
        </main>
      </div>
      <Footer />
    </>
  );
};

export default FirstPage;
